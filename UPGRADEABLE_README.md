# Netronlink Upgradeable Contract

## Overview

The Netronlink Token contract has been successfully converted to an upgradeable architecture using a custom proxy pattern. This allows for future upgrades while preserving all existing state and functionality.

## Architecture

### Components

1. **Netronlink.sol** - The implementation contract (upgraded from constructor to initializer pattern)
2. **OwnedUpgradeabilityProxy.sol** - The proxy contract that delegates calls to the implementation
3. **Deployment Scripts** - Handle the deployment and initialization of both proxy and implementation

### Key Changes Made

1. **Contract Inheritance:**
   ```solidity
   // Before
   contract Netronlink is ERC20, Ownable

   // After  
   contract Netronlink is Initializable, ERC20Upgradeable, OwnableUpgradeable
   ```

2. **Constructor → Initializer:**
   ```solidity
   // Before
   constructor(address[] memory recipients, uint256[] memory amounts, address teamAndFounder)

   // After
   function initialize(address[] memory recipients, uint256[] memory amounts, address teamAndFounder) public initializer
   ```

3. **Added Upgrade Support:**
   - Version tracking function
   - Storage gap for future variables
   - Proper initialization chain

## Deployment

### Deploy Upgradeable Version

```bash
npx hardhat deploy --tags upgradeable
```

This will:
1. Deploy the Netronlink implementation contract
2. Deploy the OwnedUpgradeabilityProxy contract
3. Set the implementation in the proxy
4. Initialize the contract with the specified parameters

### Important Addresses

After deployment, you'll get:
- **Proxy Address**: Use this address to interact with the token
- **Implementation Address**: The actual contract logic (don't interact directly)

## Usage

### Interacting with the Contract

Always use the **proxy address** to interact with the token:

```javascript
const proxyAddress = "0x..." // From deployment output
const token = await ethers.getContractAt("Netronlink", proxyAddress)

// All normal token operations work through the proxy
await token.transfer(recipient, amount)
await token.balanceOf(address)
await token.claimTeamTokens()
```

### Upgrading the Contract

```javascript
// Get proxy contract
const proxy = await ethers.getContractAt("OwnedUpgradeabilityProxy", proxyAddress)

// Deploy new implementation
const newImplementation = await ethers.deployContract("NetronlinkV2")

// Upgrade (only proxy owner can do this)
await proxy.upgradeTo(newImplementation.target)
```

### Maintenance Mode

The proxy supports a maintenance mode that restricts access:

```javascript
// Enable maintenance (only proxy owner)
await proxy.setMaintenance(true)

// During maintenance, only proxy owner can interact
// Disable maintenance
await proxy.setMaintenance(false)
```

## Testing

### Run All Tests

```bash
# Original functionality tests
npx hardhat test test/unit/token.test.js

# Upgradeable-specific tests  
npx hardhat test test/unit/upgradeable.test.js

# Core functionality on upgradeable version
npx hardhat test test/unit/token-upgradeable.test.js
```

### Test Results

✅ **31 passing tests** for core functionality
✅ **12 passing tests** for proxy functionality  
✅ **32 passing tests** for upgradeable version functionality

## Key Features Preserved

All original functionality is preserved:

- ✅ ERC20 token functionality
- ✅ 1% burn mechanism on transfers (with whitelist exemption)
- ✅ Team token vesting (24-month schedule with 1-year cliff)
- ✅ Whitelist management
- ✅ Owner controls
- ✅ All events and getter functions

## New Features Added

### Upgradeability

- **Upgrade Function**: Proxy owner can upgrade implementation
- **State Preservation**: All balances and state preserved during upgrades
- **Access Control**: Only proxy owner can perform upgrades

### Maintenance Mode

- **Emergency Control**: Proxy owner can enable maintenance mode
- **Restricted Access**: During maintenance, only proxy owner can interact
- **Quick Recovery**: Can be disabled immediately when issues are resolved

### Version Tracking

- **Version Function**: `version()` returns current contract version
- **Upgrade History**: Can track which version is currently deployed

## Security Considerations

### Proxy Owner Powers

The proxy owner has significant control:
- Can upgrade the implementation contract
- Can enable/disable maintenance mode
- Can transfer proxy ownership

### Best Practices

1. **Use Multi-sig**: Proxy owner should be a multi-signature wallet
2. **Test Upgrades**: Always test upgrades on testnet first
3. **Gradual Rollout**: Consider using maintenance mode during sensitive upgrades
4. **Monitor State**: Verify state preservation after each upgrade

### Storage Layout

The contract uses storage gaps to prevent conflicts in future upgrades:

```solidity
uint256[45] private __gap;
```

## Example Upgrade Script

```bash
npx hardhat run scripts/upgrade-example.js
```

This demonstrates:
- Deploying a new implementation
- Performing the upgrade
- Verifying state preservation
- Testing functionality post-upgrade

## Contract Addresses

### Testnet Deployment
- **Proxy**: `******************************************`
- **Implementation**: `******************************************`

### Mainnet Deployment
*To be updated when deployed to mainnet*

## Support

For questions about the upgradeable implementation:
1. Review the test files for usage examples
2. Check the deployment scripts for setup procedures
3. Run the upgrade example script to see the process in action

## Version History

- **v1.0.0**: Initial upgradeable implementation
  - Converted from constructor to initializer pattern
  - Added proxy architecture
  - Preserved all original functionality
  - Added maintenance mode and version tracking 