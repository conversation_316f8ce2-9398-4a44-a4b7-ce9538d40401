{"address": "0x5FbDB2315678afecb367f032d93F642f64180aa3", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "presaleWallet", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "PresaleTokensClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TeamTokensClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensBurned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "WhitelistRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "Whitelisted", "type": "event"}, {"inputs": [], "name": "CLIFF", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MONTHLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_MONTHS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimTeamTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getClaimableTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "address", "name": "teamAndFounder", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "teamVesting", "outputs": [{"internalType": "uint256", "name": "totalAllocated", "type": "uint256"}, {"internalType": "uint256", "name": "claimed", "type": "uint256"}, {"internalType": "uint64", "name": "startTime", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "vestingRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "transactionHash": "0x25cbc34d4c3971b0b609d2f7673147dede31d8bbf80edc127a15e7108f350466", "receipt": {"to": null, "from": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "contractAddress": "0x5FbDB2315678afecb367f032d93F642f64180aa3", "transactionIndex": 0, "gasUsed": "2843758", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xff4ce047f459a195183e42acf920130d4f5344a243b725c9481c9087a74d74dc", "transactionHash": "0x25cbc34d4c3971b0b609d2f7673147dede31d8bbf80edc127a15e7108f350466", "logs": [], "blockNumber": 1, "cumulativeGasUsed": "2843758", "status": 1, "byzantium": true}, "args": [], "numDeployments": 1, "solcInputHash": "8d9be57fe434434970d13737f3a45c5f", "metadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"version\",\"type\":\"uint8\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"presaleWallet\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"PresaleTokensClaimed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TeamTokensClaimed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TokensBurned\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"WhitelistRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Whitelisted\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"CLIFF\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_SUPPLY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MONTHLY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"TOTAL_MONTHS\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"addWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"claimTeamTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"subtractedValue\",\"type\":\"uint256\"}],\"name\":\"decreaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getClaimableTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"addedValue\",\"type\":\"uint256\"}],\"name\":\"increaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"recipients\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"address\",\"name\":\"teamAndFounder\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"removeWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"teamVesting\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalAllocated\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"claimed\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"startTime\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"version\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vestingRecipient\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Initialized(uint8)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"See {IERC20-allowance}.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"See {IERC20-balanceOf}.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"decreaseAllowance(address,uint256)\":{\"details\":\"Atomically decreases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address. - `spender` must have allowance for the caller of at least `subtractedValue`.\"},\"increaseAllowance(address,uint256)\":{\"details\":\"Atomically increases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"See {IERC20-totalSupply}.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `amount`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `amount`. - the caller must have allowance for ``from``'s tokens of at least `amount`.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/Netronlink.sol\":\"Netronlink\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../utils/ContextUpgradeable.sol\\\";\\nimport \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * By default, the owner account will be the one that deploys the contract. This\\n * can later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract OwnableUpgradeable is Initializable, ContextUpgradeable {\\n    address private _owner;\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial owner.\\n     */\\n    function __Ownable_init() internal onlyInitializing {\\n        __Ownable_init_unchained();\\n    }\\n\\n    function __Ownable_init_unchained() internal onlyInitializing {\\n        _transferOwnership(_msgSender());\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        return _owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        require(owner() == _msgSender(), \\\"Ownable: caller is not the owner\\\");\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        require(newOwner != address(0), \\\"Ownable: new owner is the zero address\\\");\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        address oldOwner = _owner;\\n        _owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[49] private __gap;\\n}\\n\",\"keccak256\":\"0x4075622496acc77fd6d4de4cc30a8577a744d5c75afad33fdeacf1704d6eda98\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (proxy/utils/Initializable.sol)\\n\\npragma solidity ^0.8.2;\\n\\nimport \\\"../../utils/AddressUpgradeable.sol\\\";\\n\\n/**\\n * @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\\n * behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\\n * external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\\n * function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\\n *\\n * The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\\n * reused. This mechanism prevents re-execution of each \\\"step\\\" but allows the creation of new initialization steps in\\n * case an upgrade adds a module that needs to be initialized.\\n *\\n * For example:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```solidity\\n * contract MyToken is ERC20Upgradeable {\\n *     function initialize() initializer public {\\n *         __ERC20_init(\\\"MyToken\\\", \\\"MTK\\\");\\n *     }\\n * }\\n *\\n * contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\\n *     function initializeV2() reinitializer(2) public {\\n *         __ERC20Permit_init(\\\"MyToken\\\");\\n *     }\\n * }\\n * ```\\n *\\n * TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\\n * possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\\n *\\n * CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\\n * that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\\n *\\n * [CAUTION]\\n * ====\\n * Avoid leaving a contract uninitialized.\\n *\\n * An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\\n * contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\\n * the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```\\n * /// @custom:oz-upgrades-unsafe-allow constructor\\n * constructor() {\\n *     _disableInitializers();\\n * }\\n * ```\\n * ====\\n */\\nabstract contract Initializable {\\n    /**\\n     * @dev Indicates that the contract has been initialized.\\n     * @custom:oz-retyped-from bool\\n     */\\n    uint8 private _initialized;\\n\\n    /**\\n     * @dev Indicates that the contract is in the process of being initialized.\\n     */\\n    bool private _initializing;\\n\\n    /**\\n     * @dev Triggered when the contract has been initialized or reinitialized.\\n     */\\n    event Initialized(uint8 version);\\n\\n    /**\\n     * @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\\n     * `onlyInitializing` functions can be used to initialize parent contracts.\\n     *\\n     * Similar to `reinitializer(1)`, except that functions marked with `initializer` can be nested in the context of a\\n     * constructor.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier initializer() {\\n        bool isTopLevelCall = !_initializing;\\n        require(\\n            (isTopLevelCall && _initialized < 1) || (!AddressUpgradeable.isContract(address(this)) && _initialized == 1),\\n            \\\"Initializable: contract is already initialized\\\"\\n        );\\n        _initialized = 1;\\n        if (isTopLevelCall) {\\n            _initializing = true;\\n        }\\n        _;\\n        if (isTopLevelCall) {\\n            _initializing = false;\\n            emit Initialized(1);\\n        }\\n    }\\n\\n    /**\\n     * @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\\n     * contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\\n     * used to initialize parent contracts.\\n     *\\n     * A reinitializer may be used after the original initialization step. This is essential to configure modules that\\n     * are added through upgrades and that require initialization.\\n     *\\n     * When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\\n     * cannot be nested. If one is invoked in the context of another, execution will revert.\\n     *\\n     * Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\\n     * a contract, executing them in the right order is up to the developer or operator.\\n     *\\n     * WARNING: setting the version to 255 will prevent any future reinitialization.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier reinitializer(uint8 version) {\\n        require(!_initializing && _initialized < version, \\\"Initializable: contract is already initialized\\\");\\n        _initialized = version;\\n        _initializing = true;\\n        _;\\n        _initializing = false;\\n        emit Initialized(version);\\n    }\\n\\n    /**\\n     * @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\\n     * {initializer} and {reinitializer} modifiers, directly or indirectly.\\n     */\\n    modifier onlyInitializing() {\\n        require(_initializing, \\\"Initializable: contract is not initializing\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\\n     * Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\\n     * to any version. It is recommended to use this to lock implementation contracts that are designed to be called\\n     * through proxies.\\n     *\\n     * Emits an {Initialized} event the first time it is successfully executed.\\n     */\\n    function _disableInitializers() internal virtual {\\n        require(!_initializing, \\\"Initializable: contract is initializing\\\");\\n        if (_initialized != type(uint8).max) {\\n            _initialized = type(uint8).max;\\n            emit Initialized(type(uint8).max);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the highest version that has been initialized. See {reinitializer}.\\n     */\\n    function _getInitializedVersion() internal view returns (uint8) {\\n        return _initialized;\\n    }\\n\\n    /**\\n     * @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}.\\n     */\\n    function _isInitializing() internal view returns (bool) {\\n        return _initializing;\\n    }\\n}\\n\",\"keccak256\":\"0x89be10e757d242e9b18d5a32c9fbe2019f6d63052bbe46397a430a1d60d7f794\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (token/ERC20/ERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC20Upgradeable.sol\\\";\\nimport \\\"./extensions/IERC20MetadataUpgradeable.sol\\\";\\nimport \\\"../../utils/ContextUpgradeable.sol\\\";\\nimport \\\"../../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC20} interface.\\n *\\n * This implementation is agnostic to the way tokens are created. This means\\n * that a supply mechanism has to be added in a derived contract using {_mint}.\\n * For a generic mechanism see {ERC20PresetMinterPauser}.\\n *\\n * TIP: For a detailed writeup see our guide\\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\\n * to implement supply mechanisms].\\n *\\n * The default value of {decimals} is 18. To change this, you should override\\n * this function so it returns a different value.\\n *\\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\\n * instead returning `false` on failure. This behavior is nonetheless\\n * conventional and does not conflict with the expectations of ERC20\\n * applications.\\n *\\n * Additionally, an {Approval} event is emitted on calls to {transferFrom}.\\n * This allows applications to reconstruct the allowance for all accounts just\\n * by listening to said events. Other implementations of the EIP may not emit\\n * these events, as it isn't required by the specification.\\n *\\n * Finally, the non-standard {decreaseAllowance} and {increaseAllowance}\\n * functions have been added to mitigate the well-known issues around setting\\n * allowances. See {IERC20-approve}.\\n */\\ncontract ERC20Upgradeable is Initializable, ContextUpgradeable, IERC20Upgradeable, IERC20MetadataUpgradeable {\\n    mapping(address => uint256) private _balances;\\n\\n    mapping(address => mapping(address => uint256)) private _allowances;\\n\\n    uint256 private _totalSupply;\\n\\n    string private _name;\\n    string private _symbol;\\n\\n    /**\\n     * @dev Sets the values for {name} and {symbol}.\\n     *\\n     * All two of these values are immutable: they can only be set once during\\n     * construction.\\n     */\\n    function __ERC20_init(string memory name_, string memory symbol_) internal onlyInitializing {\\n        __ERC20_init_unchained(name_, symbol_);\\n    }\\n\\n    function __ERC20_init_unchained(string memory name_, string memory symbol_) internal onlyInitializing {\\n        _name = name_;\\n        _symbol = symbol_;\\n    }\\n\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() public view virtual override returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev Returns the symbol of the token, usually a shorter version of the\\n     * name.\\n     */\\n    function symbol() public view virtual override returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev Returns the number of decimals used to get its user representation.\\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n     * be displayed to a user as `5.05` (`505 / 10 ** 2`).\\n     *\\n     * Tokens usually opt for a value of 18, imitating the relationship between\\n     * Ether and Wei. This is the default value returned by this function, unless\\n     * it's overridden.\\n     *\\n     * NOTE: This information is only used for _display_ purposes: it in\\n     * no way affects any of the arithmetic of the contract, including\\n     * {IERC20-balanceOf} and {IERC20-transfer}.\\n     */\\n    function decimals() public view virtual override returns (uint8) {\\n        return 18;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-totalSupply}.\\n     */\\n    function totalSupply() public view virtual override returns (uint256) {\\n        return _totalSupply;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-balanceOf}.\\n     */\\n    function balanceOf(address account) public view virtual override returns (uint256) {\\n        return _balances[account];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transfer}.\\n     *\\n     * Requirements:\\n     *\\n     * - `to` cannot be the zero address.\\n     * - the caller must have a balance of at least `amount`.\\n     */\\n    function transfer(address to, uint256 amount) public virtual override returns (bool) {\\n        address owner = _msgSender();\\n        _transfer(owner, to, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-allowance}.\\n     */\\n    function allowance(address owner, address spender) public view virtual override returns (uint256) {\\n        return _allowances[owner][spender];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-approve}.\\n     *\\n     * NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on\\n     * `transferFrom`. This is semantically equivalent to an infinite approval.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     */\\n    function approve(address spender, uint256 amount) public virtual override returns (bool) {\\n        address owner = _msgSender();\\n        _approve(owner, spender, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transferFrom}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance. This is not\\n     * required by the EIP. See the note at the beginning of {ERC20}.\\n     *\\n     * NOTE: Does not update the allowance if the current allowance\\n     * is the maximum `uint256`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` and `to` cannot be the zero address.\\n     * - `from` must have a balance of at least `amount`.\\n     * - the caller must have allowance for ``from``'s tokens of at least\\n     * `amount`.\\n     */\\n    function transferFrom(address from, address to, uint256 amount) public virtual override returns (bool) {\\n        address spender = _msgSender();\\n        _spendAllowance(from, spender, amount);\\n        _transfer(from, to, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Atomically increases the allowance granted to `spender` by the caller.\\n     *\\n     * This is an alternative to {approve} that can be used as a mitigation for\\n     * problems described in {IERC20-approve}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     */\\n    function increaseAllowance(address spender, uint256 addedValue) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        _approve(owner, spender, allowance(owner, spender) + addedValue);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Atomically decreases the allowance granted to `spender` by the caller.\\n     *\\n     * This is an alternative to {approve} that can be used as a mitigation for\\n     * problems described in {IERC20-approve}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     * - `spender` must have allowance for the caller of at least\\n     * `subtractedValue`.\\n     */\\n    function decreaseAllowance(address spender, uint256 subtractedValue) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        uint256 currentAllowance = allowance(owner, spender);\\n        require(currentAllowance >= subtractedValue, \\\"ERC20: decreased allowance below zero\\\");\\n        unchecked {\\n            _approve(owner, spender, currentAllowance - subtractedValue);\\n        }\\n\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Moves `amount` of tokens from `from` to `to`.\\n     *\\n     * This internal function is equivalent to {transfer}, and can be used to\\n     * e.g. implement automatic token fees, slashing mechanisms, etc.\\n     *\\n     * Emits a {Transfer} event.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `from` must have a balance of at least `amount`.\\n     */\\n    function _transfer(address from, address to, uint256 amount) internal virtual {\\n        require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n        require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n        _beforeTokenTransfer(from, to, amount);\\n\\n        uint256 fromBalance = _balances[from];\\n        require(fromBalance >= amount, \\\"ERC20: transfer amount exceeds balance\\\");\\n        unchecked {\\n            _balances[from] = fromBalance - amount;\\n            // Overflow not possible: the sum of all balances is capped by totalSupply, and the sum is preserved by\\n            // decrementing then incrementing.\\n            _balances[to] += amount;\\n        }\\n\\n        emit Transfer(from, to, amount);\\n\\n        _afterTokenTransfer(from, to, amount);\\n    }\\n\\n    /** @dev Creates `amount` tokens and assigns them to `account`, increasing\\n     * the total supply.\\n     *\\n     * Emits a {Transfer} event with `from` set to the zero address.\\n     *\\n     * Requirements:\\n     *\\n     * - `account` cannot be the zero address.\\n     */\\n    function _mint(address account, uint256 amount) internal virtual {\\n        require(account != address(0), \\\"ERC20: mint to the zero address\\\");\\n\\n        _beforeTokenTransfer(address(0), account, amount);\\n\\n        _totalSupply += amount;\\n        unchecked {\\n            // Overflow not possible: balance + amount is at most totalSupply + amount, which is checked above.\\n            _balances[account] += amount;\\n        }\\n        emit Transfer(address(0), account, amount);\\n\\n        _afterTokenTransfer(address(0), account, amount);\\n    }\\n\\n    /**\\n     * @dev Destroys `amount` tokens from `account`, reducing the\\n     * total supply.\\n     *\\n     * Emits a {Transfer} event with `to` set to the zero address.\\n     *\\n     * Requirements:\\n     *\\n     * - `account` cannot be the zero address.\\n     * - `account` must have at least `amount` tokens.\\n     */\\n    function _burn(address account, uint256 amount) internal virtual {\\n        require(account != address(0), \\\"ERC20: burn from the zero address\\\");\\n\\n        _beforeTokenTransfer(account, address(0), amount);\\n\\n        uint256 accountBalance = _balances[account];\\n        require(accountBalance >= amount, \\\"ERC20: burn amount exceeds balance\\\");\\n        unchecked {\\n            _balances[account] = accountBalance - amount;\\n            // Overflow not possible: amount <= accountBalance <= totalSupply.\\n            _totalSupply -= amount;\\n        }\\n\\n        emit Transfer(account, address(0), amount);\\n\\n        _afterTokenTransfer(account, address(0), amount);\\n    }\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the `owner` s tokens.\\n     *\\n     * This internal function is equivalent to `approve`, and can be used to\\n     * e.g. set automatic allowances for certain subsystems, etc.\\n     *\\n     * Emits an {Approval} event.\\n     *\\n     * Requirements:\\n     *\\n     * - `owner` cannot be the zero address.\\n     * - `spender` cannot be the zero address.\\n     */\\n    function _approve(address owner, address spender, uint256 amount) internal virtual {\\n        require(owner != address(0), \\\"ERC20: approve from the zero address\\\");\\n        require(spender != address(0), \\\"ERC20: approve to the zero address\\\");\\n\\n        _allowances[owner][spender] = amount;\\n        emit Approval(owner, spender, amount);\\n    }\\n\\n    /**\\n     * @dev Updates `owner` s allowance for `spender` based on spent `amount`.\\n     *\\n     * Does not update the allowance amount in case of infinite allowance.\\n     * Revert if not enough allowance is available.\\n     *\\n     * Might emit an {Approval} event.\\n     */\\n    function _spendAllowance(address owner, address spender, uint256 amount) internal virtual {\\n        uint256 currentAllowance = allowance(owner, spender);\\n        if (currentAllowance != type(uint256).max) {\\n            require(currentAllowance >= amount, \\\"ERC20: insufficient allowance\\\");\\n            unchecked {\\n                _approve(owner, spender, currentAllowance - amount);\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Hook that is called before any transfer of tokens. This includes\\n     * minting and burning.\\n     *\\n     * Calling conditions:\\n     *\\n     * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n     * will be transferred to `to`.\\n     * - when `from` is zero, `amount` tokens will be minted for `to`.\\n     * - when `to` is zero, `amount` of ``from``'s tokens will be burned.\\n     * - `from` and `to` are never both zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _beforeTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n    /**\\n     * @dev Hook that is called after any transfer of tokens. This includes\\n     * minting and burning.\\n     *\\n     * Calling conditions:\\n     *\\n     * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n     * has been transferred to `to`.\\n     * - when `from` is zero, `amount` tokens have been minted for `to`.\\n     * - when `to` is zero, `amount` of ``from``'s tokens have been burned.\\n     * - `from` and `to` are never both zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _afterTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[45] private __gap;\\n}\\n\",\"keccak256\":\"0xd14a627157b9a411d2410713e5dd3a377e9064bd5c194a90748bbf27ea625784\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20Upgradeable {\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n    /**\\n     * @dev Returns the amount of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the amount of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves `amount` tokens from the caller's account to `to`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address to, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Moves `amount` tokens from `from` to `to` using the\\n     * allowance mechanism. `amount` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\",\"keccak256\":\"0x0e1f0f5f62f67a881cd1a9597acbc0a5e4071f3c2c10449a183b922ae7272e3f\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/extensions/IERC20MetadataUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20Upgradeable.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC20 standard.\\n *\\n * _Available since v4.1._\\n */\\ninterface IERC20MetadataUpgradeable is IERC20Upgradeable {\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the symbol of the token.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the decimals places of the token.\\n     */\\n    function decimals() external view returns (uint8);\\n}\\n\",\"keccak256\":\"0x605434219ebbe4653f703640f06969faa5a1d78f0bfef878e5ddbb1ca369ceeb\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (utils/Address.sol)\\n\\npragma solidity ^0.8.1;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary AddressUpgradeable {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following\\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     *\\n     * Furthermore, `isContract` will also return true if the target contract within\\n     * the same transaction is already scheduled for destruction by `SELFDESTRUCT`,\\n     * which only has an effect at the end of a transaction.\\n     * ====\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * You shouldn't rely on `isContract` to protect against flash loan attacks!\\n     *\\n     * Preventing calls from contracts is highly discouraged. It breaks composability, breaks support for smart wallets\\n     * like Gnosis Safe, and does not provide security since it can be circumvented by calling from a contract\\n     * constructor.\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // This method relies on extcodesize/address.code.length, which returns 0\\n        // for contracts in construction, since the code is only stored at the end\\n        // of the constructor execution.\\n\\n        return account.code.length > 0;\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://consensys.net/diligence/blog/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.8.0/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        (bool success, ) = recipient.call{value: amount}(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n\\n    /**\\n     * @dev Performs a Solidity function call using a low level `call`. A\\n     * plain `call` is an unsafe replacement for a function call: use this\\n     * function instead.\\n     *\\n     * If `target` reverts with a revert reason, it is bubbled up by this\\n     * function (like regular Solidity function calls).\\n     *\\n     * Returns the raw returned data. To convert to the expected return value,\\n     * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].\\n     *\\n     * Requirements:\\n     *\\n     * - `target` must be a contract.\\n     * - calling `target` with `data` must not revert.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, \\\"Address: low-level call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with\\n     * `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but also transferring `value` wei to `target`.\\n     *\\n     * Requirements:\\n     *\\n     * - the calling contract must have an ETH balance of at least `value`.\\n     * - the called Solidity function must be `payable`.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(address target, bytes memory data, uint256 value) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, value, \\\"Address: low-level call with value failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but\\n     * with `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(\\n        address target,\\n        bytes memory data,\\n        uint256 value,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        require(address(this).balance >= value, \\\"Address: insufficient balance for call\\\");\\n        (bool success, bytes memory returndata) = target.call{value: value}(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {\\n        return functionStaticCall(target, data, \\\"Address: low-level static call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.staticcall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionDelegateCall(target, data, \\\"Address: low-level delegate call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.delegatecall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call to smart-contract was successful, and revert (either by bubbling\\n     * the revert reason or using the provided one) in case of unsuccessful call or if target was not a contract.\\n     *\\n     * _Available since v4.8._\\n     */\\n    function verifyCallResultFromTarget(\\n        address target,\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        if (success) {\\n            if (returndata.length == 0) {\\n                // only check isContract if the call was successful and the return data is empty\\n                // otherwise we already know that it was a contract\\n                require(isContract(target), \\\"Address: call to non-contract\\\");\\n            }\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call was successful, and revert if it wasn't, either by bubbling the\\n     * revert reason or using the provided one.\\n     *\\n     * _Available since v4.3._\\n     */\\n    function verifyCallResult(\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal pure returns (bytes memory) {\\n        if (success) {\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    function _revert(bytes memory returndata, string memory errorMessage) private pure {\\n        // Look for revert reason and bubble it up if present\\n        if (returndata.length > 0) {\\n            // The easiest way to bubble the revert reason is using memory via assembly\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                let returndata_size := mload(returndata)\\n                revert(add(32, returndata), returndata_size)\\n            }\\n        } else {\\n            revert(errorMessage);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x9c80f545915582e63fe206c6ce27cbe85a86fc10b9cd2a0e8c9488fb7c2ee422\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\nimport \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract ContextUpgradeable is Initializable {\\n    function __Context_init() internal onlyInitializing {\\n    }\\n\\n    function __Context_init_unchained() internal onlyInitializing {\\n    }\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[50] private __gap;\\n}\\n\",\"keccak256\":\"0x963ea7f0b48b032eef72fe3a7582edf78408d6f834115b9feadd673a4d5bd149\",\"license\":\"MIT\"},\"contracts/Netronlink.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.28;\\n// final\\n\\nimport \\\"@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\\\";\\n\\ncontract Netronlink is Initializable, ERC20Upgradeable, OwnableUpgradeable {\\n    uint256 public constant MAX_SUPPLY = 80000000 * 1e18;\\n    uint256 public constant CLIFF = 365 days;\\n    uint256 public constant MONTHLY = 30 days;\\n    uint256 public constant TOTAL_MONTHS = 24;\\n    uint256 internal constant teamAmount = ******** * 1e18;\\n    address public vestingRecipient;\\n\\n    mapping(address => bool) isWhitelisted;\\n\\n    //Struct\\n    struct VestingSchedule {\\n        uint256 totalAllocated;\\n        uint256 claimed;\\n        uint64 startTime;\\n    }\\n    VestingSchedule public teamVesting;\\n\\n    //EVENTS--\\n    event Whitelisted(address indexed account);\\n    event WhitelistRemoved(address indexed account);\\n    event TeamTokensClaimed(address indexed recipient, uint256 amount);\\n    event TokensBurned(address indexed from, uint256 amount);\\n    event PresaleTokensClaimed(address presaleWallet, uint256 amount); // \\u2705 New event\\n\\n    function initialize(\\n        address[] memory recipients,\\n        uint256[] memory amounts,\\n        address teamAndFounder\\n    ) public initializer {\\n        __ERC20_init(\\\"Netronlink Token\\\", \\\"NTL\\\");\\n        __Ownable_init();\\n\\n        require(recipients.length == amounts.length, \\\"Mismatched input arrays\\\");\\n\\n        uint256 totalToMint = 0;\\n        for (uint256 i = 0; i < amounts.length; i++) {\\n            totalToMint += amounts[i];\\n        }\\n\\n        require(totalToMint <= MAX_SUPPLY, \\\"Exceeds max supply\\\");\\n\\n        for (uint256 i = 0; i < recipients.length; i++) {\\n            _mint(recipients[i], amounts[i]);\\n        }\\n\\n        _mint(address(this), teamAmount);\\n\\n        teamVesting = VestingSchedule({\\n            totalAllocated: teamAmount,\\n            claimed: 0,\\n            startTime: uint64(block.timestamp)\\n        });\\n        vestingRecipient = teamAndFounder;\\n        addWhitelist(address(this));\\n    }\\n\\n    function _transfer(address from, address to, uint256 amount) internal override {\\n        require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n        require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n        uint256 burnAmount = 0;\\n        uint256 sendAmount = amount;\\n\\n        if (!isWhitelisted[from] && !isWhitelisted[to]) {\\n            burnAmount = amount / 100; // 1%\\n            sendAmount = amount - burnAmount;\\n        }\\n\\n        // If there's a burn, handle it first by reducing the from balance\\n        if (burnAmount > 0) {\\n            _burn(from, burnAmount);\\n            emit TokensBurned(from, burnAmount); // burn log\\n        }\\n        \\n        // Call parent _transfer for the send amount\\n        super._transfer(from, to, sendAmount);\\n    }\\n\\n    function addWhitelist(address user) public onlyOwner {\\n        isWhitelisted[user] = true;\\n        emit Whitelisted(user);\\n    }\\n\\n    function removeWhitelist(address user) public onlyOwner {\\n        isWhitelisted[user] = false;\\n        emit WhitelistRemoved(user);\\n    }\\n\\n    function claimTeamTokens() external {\\n        require(msg.sender == vestingRecipient, \\\"Not authorized\\\");\\n\\n        uint256 claimable = _calculateClaimable();\\n        require(claimable > 0, \\\"Nothing to claim\\\");\\n\\n        teamVesting.claimed += claimable;\\n        _transfer(address(this), msg.sender, claimable);\\n\\n        emit TeamTokensClaimed(msg.sender, claimable);\\n    }\\n\\n    function _calculateClaimable() internal view returns (uint256 claimable) {\\n        VestingSchedule storage vest = teamVesting;\\n\\n        if (block.timestamp < vest.startTime + CLIFF) {\\n            return 0; // still in cliff\\n        }\\n\\n        uint256 monthsElapsed = (block.timestamp - vest.startTime - CLIFF) / MONTHLY;\\n        if (monthsElapsed > TOTAL_MONTHS) monthsElapsed = TOTAL_MONTHS;\\n\\n        uint256 totalVested = (vest.totalAllocated * monthsElapsed) / TOTAL_MONTHS;\\n        if (monthsElapsed == TOTAL_MONTHS) {\\n            totalVested = vest.totalAllocated;\\n        }\\n\\n        if (totalVested <= vest.claimed) {\\n            return 0;\\n        }\\n\\n        return totalVested - vest.claimed;\\n    }\\n\\n    function getClaimableTokens() external view returns (uint256) { \\n        require(msg.sender == vestingRecipient, \\\"Not authorized\\\");\\n        return _calculateClaimable();\\n    }\\n\\n    // Version function for upgrade tracking\\n    function version() external pure returns (string memory) {\\n        return \\\"1.0.0\\\";\\n    }\\n\\n    // Gap for future storage variables in upgrades\\n    uint256[45] private __gap;\\n}\\n\",\"keccak256\":\"0x81ec4d548d2eb184e4f826f0a21c3183f15ec18ed36866e7c0e220b1d90948ce\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b506004361061018e5760003560e01c8063715018a6116100de578063b87592f211610097578063dd62ed3e11610071578063dd62ed3e14610469578063f2fde38b14610499578063f522738c146104b5578063f80f5dd5146104d35761018e565b8063b87592f214610411578063b9d52f711461042f578063c194ee2d1461044b5761018e565b8063715018a61461034f57806378c8cda7146103595780638da5cb5b1461037557806395d89b4114610393578063a457c2d7146103b1578063a9059cbb146103e15761018e565b8063313ce5671161014b578063395093511161012557806339509351146102b157806354fd4d50146102e1578063619f3d6c146102ff57806370a082311461031f5761018e565b8063313ce5671461025757806332cb6b0c14610275578063339f0eaf146102935761018e565b806306fdde0314610193578063095ea7b3146101b157806318160ddd146101e157806319fe14ce146101ff57806323b872dd1461021d5780632af5356a1461024d575b600080fd5b61019b6104ef565b6040516101a89190611f36565b60405180910390f35b6101cb60048036038101906101c69190612000565b610581565b6040516101d8919061205b565b60405180910390f35b6101e96105a4565b6040516101f69190612085565b60405180910390f35b6102076105ae565b6040516102149190612085565b60405180910390f35b610237600480360381019061023291906120a0565b6105b5565b604051610244919061205b565b60405180910390f35b6102556105e4565b005b61025f61073b565b60405161026c919061210f565b60405180910390f35b61027d610744565b60405161028a9190612085565b60405180910390f35b61029b610753565b6040516102a89190612139565b60405180910390f35b6102cb60048036038101906102c69190612000565b610779565b6040516102d8919061205b565b60405180910390f35b6102e96107b0565b6040516102f69190611f36565b60405180910390f35b6103076107ed565b60405161031693929190612177565b60405180910390f35b610339600480360381019061033491906121ae565b610819565b6040516103469190612085565b60405180910390f35b610357610862565b005b610373600480360381019061036e91906121ae565b610876565b005b61037d61091c565b60405161038a9190612139565b60405180910390f35b61039b610946565b6040516103a89190611f36565b60405180910390f35b6103cb60048036038101906103c69190612000565b6109d8565b6040516103d8919061205b565b60405180910390f35b6103fb60048036038101906103f69190612000565b610a4f565b604051610408919061205b565b60405180910390f35b610419610a72565b6040516104269190612085565b60405180910390f35b610449600480360381019061044491906123e6565b610a7a565b005b610453610e35565b6040516104609190612085565b60405180910390f35b610483600480360381019061047e9190612471565b610ed4565b6040516104909190612085565b60405180910390f35b6104b360048036038101906104ae91906121ae565b610f5b565b005b6104bd610fde565b6040516104ca9190612085565b60405180910390f35b6104ed60048036038101906104e891906121ae565b610fe3565b005b6060603680546104fe906124e0565b80601f016020809104026020016040519081016040528092919081815260200182805461052a906124e0565b80156105775780601f1061054c57610100808354040283529160200191610577565b820191906000526020600020905b81548152906001019060200180831161055a57829003601f168201915b5050505050905090565b60008061058c611089565b9050610599818585611091565b600191505092915050565b6000603554905090565b62278d0081565b6000806105c0611089565b90506105cd85828561125a565b6105d88585856112e6565b60019150509392505050565b609760009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610674576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161066b9061255d565b60405180910390fd5b600061067e611505565b9050600081116106c3576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016106ba906125c9565b60405180910390fd5b80609960010160008282546106d89190612618565b925050819055506106ea3033836112e6565b3373ffffffffffffffffffffffffffffffffffffffff167f32a7537ee28ab9e44edfe4f55db5f08bf3a2bc8c09f2b5e0eaf20715d2609eab826040516107309190612085565b60405180910390a250565b60006012905090565b6a422ca8b0a00a425000000081565b609760009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b600080610784611089565b90506107a58185856107968589610ed4565b6107a09190612618565b611091565b600191505092915050565b60606040518060400160405280600581526020017f312e302e30000000000000000000000000000000000000000000000000000000815250905090565b60998060000154908060010154908060020160009054906101000a900467ffffffffffffffff16905083565b6000603360008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020549050919050565b61086a61160c565b610874600061168a565b565b61087e61160c565b6000609860008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055508073ffffffffffffffffffffffffffffffffffffffff167fde8cf212af7ce38b2840785a2768d97ff2dbf3c21b516961cec0061e134c2a1e60405160405180910390a250565b6000606560009054906101000a900473ffffffffffffffffffffffffffffffffffffffff16905090565b606060378054610955906124e0565b80601f0160208091040260200160405190810160405280929190818152602001828054610981906124e0565b80156109ce5780601f106109a3576101008083540402835291602001916109ce565b820191906000526020600020905b8154815290600101906020018083116109b157829003601f168201915b5050505050905090565b6000806109e3611089565b905060006109f18286610ed4565b905083811015610a36576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610a2d906126be565b60405180910390fd5b610a438286868403611091565b60019250505092915050565b600080610a5a611089565b9050610a678185856112e6565b600191505092915050565b6301e1338081565b60008060019054906101000a900460ff16159050808015610aab5750600160008054906101000a900460ff1660ff16105b80610ad85750610aba30611750565b158015610ad75750600160008054906101000a900460ff1660ff16145b5b610b17576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610b0e90612750565b60405180910390fd5b60016000806101000a81548160ff021916908360ff1602179055508015610b54576001600060016101000a81548160ff0219169083151502179055505b610bc86040518060400160405280601081526020017f4e6574726f6e6c696e6b20546f6b656e000000000000000000000000000000008152506040518060400160405280600381526020017f4e544c0000000000000000000000000000000000000000000000000000000000815250611773565b610bd06117d0565b8251845114610c14576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610c0b906127bc565b60405180910390fd5b6000805b8451811015610c5657848181518110610c3457610c336127dc565b5b602002602001015182610c479190612618565b91508080600101915050610c18565b506a422ca8b0a00a4250000000811115610ca5576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610c9c90612857565b60405180910390fd5b60005b8551811015610cfc57610cef868281518110610cc757610cc66127dc565b5b6020026020010151868381518110610ce257610ce16127dc565b5b6020026020010151611829565b8080600101915050610ca8565b50610d12306a108b2a2c28029094000000611829565b60405180606001604052806a108b2a2c280290940000008152602001600081526020014267ffffffffffffffff168152506099600082015181600001556020820151816001015560408201518160020160006101000a81548167ffffffffffffffff021916908367ffffffffffffffff16021790555090505082609760006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550610dd530610fe3565b508015610e2f5760008060016101000a81548160ff0219169083151502179055507f7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb38474024986001604051610e2691906128bc565b60405180910390a15b50505050565b6000609760009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610ec7576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610ebe9061255d565b60405180910390fd5b610ecf611505565b905090565b6000603460008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054905092915050565b610f6361160c565b600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff1603610fd2576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fc990612949565b60405180910390fd5b610fdb8161168a565b50565b601881565b610feb61160c565b6001609860008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055508073ffffffffffffffffffffffffffffffffffffffff167faab7954e9d246b167ef88aeddad35209ca2489d95a8aeb59e288d9b19fae5a5460405160405180910390a250565b600033905090565b600073ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff1603611100576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016110f7906129db565b60405180910390fd5b600073ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff160361116f576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161116690612a6d565b60405180910390fd5b80603460008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020819055508173ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff167f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b9258360405161124d9190612085565b60405180910390a3505050565b60006112668484610ed4565b90507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff81146112e057818110156112d2576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016112c990612ad9565b60405180910390fd5b6112df8484848403611091565b5b50505050565b600073ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff1603611355576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161134c90612b6b565b60405180910390fd5b600073ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff16036113c4576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016113bb90612bfd565b60405180910390fd5b600080829050609860008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff1615801561146e5750609860008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff16155b15611491576064836114809190612c4c565b9150818361148e9190612c7d565b90505b60008211156114f3576114a48583611980565b8473ffffffffffffffffffffffffffffffffffffffff167ffd38818f5291bf0bb3a2a48aadc06ba8757865d1dabd804585338aab3009dcb6836040516114ea9190612085565b60405180910390a25b6114fe858583611b4f565b5050505050565b600080609990506301e133808160020160009054906101000a900467ffffffffffffffff1667ffffffffffffffff1661153e9190612618565b42101561154f576000915050611609565b600062278d006301e133808360020160009054906101000a900467ffffffffffffffff1667ffffffffffffffff16426115889190612c7d565b6115929190612c7d565b61159c9190612c4c565b905060188111156115ac57601890505b600060188284600001546115c09190612cb1565b6115ca9190612c4c565b9050601882036115dc57826000015490505b826001015481116115f35760009350505050611609565b8260010154816116039190612c7d565b93505050505b90565b611614611089565b73ffffffffffffffffffffffffffffffffffffffff1661163261091c565b73ffffffffffffffffffffffffffffffffffffffff1614611688576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161167f90612d3f565b60405180910390fd5b565b6000606560009054906101000a900473ffffffffffffffffffffffffffffffffffffffff16905081606560006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055508173ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e060405160405180910390a35050565b6000808273ffffffffffffffffffffffffffffffffffffffff163b119050919050565b600060019054906101000a900460ff166117c2576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016117b990612dd1565b60405180910390fd5b6117cc8282611dc8565b5050565b600060019054906101000a900460ff1661181f576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161181690612dd1565b60405180910390fd5b611827611e3b565b565b600073ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff1603611898576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161188f90612e3d565b60405180910390fd5b6118a460008383611e9c565b80603560008282546118b69190612618565b9250508190555080603360008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020600082825401925050819055508173ffffffffffffffffffffffffffffffffffffffff16600073ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef836040516119689190612085565b60405180910390a361197c60008383611ea1565b5050565b600073ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff16036119ef576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016119e690612ecf565b60405180910390fd5b6119fb82600083611e9c565b6000603360008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054905081811015611a82576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611a7990612f61565b60405180910390fd5b818103603360008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000208190555081603560008282540392505081905550600073ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef84604051611b369190612085565b60405180910390a3611b4a83600084611ea1565b505050565b600073ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff1603611bbe576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611bb590612b6b565b60405180910390fd5b600073ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff1603611c2d576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611c2490612bfd565b60405180910390fd5b611c38838383611e9c565b6000603360008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054905081811015611cbf576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611cb690612ff3565b60405180910390fd5b818103603360008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000208190555081603360008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020600082825401925050819055508273ffffffffffffffffffffffffffffffffffffffff168473ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef84604051611daf9190612085565b60405180910390a3611dc2848484611ea1565b50505050565b600060019054906101000a900460ff16611e17576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611e0e90612dd1565b60405180910390fd5b8160369081611e2691906131b5565b508060379081611e3691906131b5565b505050565b600060019054906101000a900460ff16611e8a576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611e8190612dd1565b60405180910390fd5b611e9a611e95611089565b61168a565b565b505050565b505050565b600081519050919050565b600082825260208201905092915050565b60005b83811015611ee0578082015181840152602081019050611ec5565b60008484015250505050565b6000601f19601f8301169050919050565b6000611f0882611ea6565b611f128185611eb1565b9350611f22818560208601611ec2565b611f2b81611eec565b840191505092915050565b60006020820190508181036000830152611f508184611efd565b905092915050565b6000604051905090565b600080fd5b600080fd5b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b6000611f9782611f6c565b9050919050565b611fa781611f8c565b8114611fb257600080fd5b50565b600081359050611fc481611f9e565b92915050565b6000819050919050565b611fdd81611fca565b8114611fe857600080fd5b50565b600081359050611ffa81611fd4565b92915050565b6000806040838503121561201757612016611f62565b5b600061202585828601611fb5565b925050602061203685828601611feb565b9150509250929050565b60008115159050919050565b61205581612040565b82525050565b6000602082019050612070600083018461204c565b92915050565b61207f81611fca565b82525050565b600060208201905061209a6000830184612076565b92915050565b6000806000606084860312156120b9576120b8611f62565b5b60006120c786828701611fb5565b93505060206120d886828701611fb5565b92505060406120e986828701611feb565b9150509250925092565b600060ff82169050919050565b612109816120f3565b82525050565b60006020820190506121246000830184612100565b92915050565b61213381611f8c565b82525050565b600060208201905061214e600083018461212a565b92915050565b600067ffffffffffffffff82169050919050565b61217181612154565b82525050565b600060608201905061218c6000830186612076565b6121996020830185612076565b6121a66040830184612168565b949350505050565b6000602082840312156121c4576121c3611f62565b5b60006121d284828501611fb5565b91505092915050565b600080fd5b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b61221882611eec565b810181811067ffffffffffffffff82111715612237576122366121e0565b5b80604052505050565b600061224a611f58565b9050612256828261220f565b919050565b600067ffffffffffffffff821115612276576122756121e0565b5b602082029050602081019050919050565b600080fd5b600061229f61229a8461225b565b612240565b905080838252602082019050602084028301858111156122c2576122c1612287565b5b835b818110156122eb57806122d78882611fb5565b8452602084019350506020810190506122c4565b5050509392505050565b600082601f83011261230a576123096121db565b5b813561231a84826020860161228c565b91505092915050565b600067ffffffffffffffff82111561233e5761233d6121e0565b5b602082029050602081019050919050565b600061236261235d84612323565b612240565b9050808382526020820190506020840283018581111561238557612384612287565b5b835b818110156123ae578061239a8882611feb565b845260208401935050602081019050612387565b5050509392505050565b600082601f8301126123cd576123cc6121db565b5b81356123dd84826020860161234f565b91505092915050565b6000806000606084860312156123ff576123fe611f62565b5b600084013567ffffffffffffffff81111561241d5761241c611f67565b5b612429868287016122f5565b935050602084013567ffffffffffffffff81111561244a57612449611f67565b5b612456868287016123b8565b925050604061246786828701611fb5565b9150509250925092565b6000806040838503121561248857612487611f62565b5b600061249685828601611fb5565b92505060206124a785828601611fb5565b9150509250929050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b600060028204905060018216806124f857607f821691505b60208210810361250b5761250a6124b1565b5b50919050565b7f4e6f7420617574686f72697a6564000000000000000000000000000000000000600082015250565b6000612547600e83611eb1565b915061255282612511565b602082019050919050565b600060208201905081810360008301526125768161253a565b9050919050565b7f4e6f7468696e6720746f20636c61696d00000000000000000000000000000000600082015250565b60006125b3601083611eb1565b91506125be8261257d565b602082019050919050565b600060208201905081810360008301526125e2816125a6565b9050919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052601160045260246000fd5b600061262382611fca565b915061262e83611fca565b9250828201905080821115612646576126456125e9565b5b92915050565b7f45524332303a2064656372656173656420616c6c6f77616e63652062656c6f7760008201527f207a65726f000000000000000000000000000000000000000000000000000000602082015250565b60006126a8602583611eb1565b91506126b38261264c565b604082019050919050565b600060208201905081810360008301526126d78161269b565b9050919050565b7f496e697469616c697a61626c653a20636f6e747261637420697320616c72656160008201527f647920696e697469616c697a6564000000000000000000000000000000000000602082015250565b600061273a602e83611eb1565b9150612745826126de565b604082019050919050565b600060208201905081810360008301526127698161272d565b9050919050565b7f4d69736d61746368656420696e70757420617272617973000000000000000000600082015250565b60006127a6601783611eb1565b91506127b182612770565b602082019050919050565b600060208201905081810360008301526127d581612799565b9050919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052603260045260246000fd5b7f45786365656473206d617820737570706c790000000000000000000000000000600082015250565b6000612841601283611eb1565b915061284c8261280b565b602082019050919050565b6000602082019050818103600083015261287081612834565b9050919050565b6000819050919050565b6000819050919050565b60006128a66128a161289c84612877565b612881565b6120f3565b9050919050565b6128b68161288b565b82525050565b60006020820190506128d160008301846128ad565b92915050565b7f4f776e61626c653a206e6577206f776e657220697320746865207a65726f206160008201527f6464726573730000000000000000000000000000000000000000000000000000602082015250565b6000612933602683611eb1565b915061293e826128d7565b604082019050919050565b6000602082019050818103600083015261296281612926565b9050919050565b7f45524332303a20617070726f76652066726f6d20746865207a65726f2061646460008201527f7265737300000000000000000000000000000000000000000000000000000000602082015250565b60006129c5602483611eb1565b91506129d082612969565b604082019050919050565b600060208201905081810360008301526129f4816129b8565b9050919050565b7f45524332303a20617070726f766520746f20746865207a65726f20616464726560008201527f7373000000000000000000000000000000000000000000000000000000000000602082015250565b6000612a57602283611eb1565b9150612a62826129fb565b604082019050919050565b60006020820190508181036000830152612a8681612a4a565b9050919050565b7f45524332303a20696e73756666696369656e7420616c6c6f77616e6365000000600082015250565b6000612ac3601d83611eb1565b9150612ace82612a8d565b602082019050919050565b60006020820190508181036000830152612af281612ab6565b9050919050565b7f45524332303a207472616e736665722066726f6d20746865207a65726f20616460008201527f6472657373000000000000000000000000000000000000000000000000000000602082015250565b6000612b55602583611eb1565b9150612b6082612af9565b604082019050919050565b60006020820190508181036000830152612b8481612b48565b9050919050565b7f45524332303a207472616e7366657220746f20746865207a65726f206164647260008201527f6573730000000000000000000000000000000000000000000000000000000000602082015250565b6000612be7602383611eb1565b9150612bf282612b8b565b604082019050919050565b60006020820190508181036000830152612c1681612bda565b9050919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052601260045260246000fd5b6000612c5782611fca565b9150612c6283611fca565b925082612c7257612c71612c1d565b5b828204905092915050565b6000612c8882611fca565b9150612c9383611fca565b9250828203905081811115612cab57612caa6125e9565b5b92915050565b6000612cbc82611fca565b9150612cc783611fca565b9250828202612cd581611fca565b91508282048414831517612cec57612ceb6125e9565b5b5092915050565b7f4f776e61626c653a2063616c6c6572206973206e6f7420746865206f776e6572600082015250565b6000612d29602083611eb1565b9150612d3482612cf3565b602082019050919050565b60006020820190508181036000830152612d5881612d1c565b9050919050565b7f496e697469616c697a61626c653a20636f6e7472616374206973206e6f74206960008201527f6e697469616c697a696e67000000000000000000000000000000000000000000602082015250565b6000612dbb602b83611eb1565b9150612dc682612d5f565b604082019050919050565b60006020820190508181036000830152612dea81612dae565b9050919050565b7f45524332303a206d696e7420746f20746865207a65726f206164647265737300600082015250565b6000612e27601f83611eb1565b9150612e3282612df1565b602082019050919050565b60006020820190508181036000830152612e5681612e1a565b9050919050565b7f45524332303a206275726e2066726f6d20746865207a65726f2061646472657360008201527f7300000000000000000000000000000000000000000000000000000000000000602082015250565b6000612eb9602183611eb1565b9150612ec482612e5d565b604082019050919050565b60006020820190508181036000830152612ee881612eac565b9050919050565b7f45524332303a206275726e20616d6f756e7420657863656564732062616c616e60008201527f6365000000000000000000000000000000000000000000000000000000000000602082015250565b6000612f4b602283611eb1565b9150612f5682612eef565b604082019050919050565b60006020820190508181036000830152612f7a81612f3e565b9050919050565b7f45524332303a207472616e7366657220616d6f756e742065786365656473206260008201527f616c616e63650000000000000000000000000000000000000000000000000000602082015250565b6000612fdd602683611eb1565b9150612fe882612f81565b604082019050919050565b6000602082019050818103600083015261300c81612fd0565b9050919050565b60008190508160005260206000209050919050565b60006020601f8301049050919050565b600082821b905092915050565b6000600883026130757fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff82613038565b61307f8683613038565b95508019841693508086168417925050509392505050565b60006130b26130ad6130a884611fca565b612881565b611fca565b9050919050565b6000819050919050565b6130cc83613097565b6130e06130d8826130b9565b848454613045565b825550505050565b600090565b6130f56130e8565b6131008184846130c3565b505050565b5b81811015613124576131196000826130ed565b600181019050613106565b5050565b601f8211156131695761313a81613013565b61314384613028565b81016020851015613152578190505b61316661315e85613028565b830182613105565b50505b505050565b600082821c905092915050565b600061318c6000198460080261316e565b1980831691505092915050565b60006131a5838361317b565b9150826002028217905092915050565b6131be82611ea6565b67ffffffffffffffff8111156131d7576131d66121e0565b5b6131e182546124e0565b6131ec828285613128565b600060209050601f83116001811461321f576000841561320d578287015190505b6132178582613199565b86555061327f565b601f19841661322d86613013565b60005b8281101561325557848901518255600182019150602085019450602081019050613230565b86831015613272578489015161326e601f89168261317b565b8355505b6001600288020188555050505b50505050505056fea2646970667358221220928ce5e931ee98143bcacb95ace168298fd18547381cd918a2676163502b315364736f6c634300081c0033", "devdoc": {"events": {"Approval(address,address,uint256)": {"details": "Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance."}, "Initialized(uint8)": {"details": "Triggered when the contract has been initialized or reinitialized."}, "Transfer(address,address,uint256)": {"details": "Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero."}}, "kind": "dev", "methods": {"allowance(address,address)": {"details": "See {IERC20-allowance}."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "See {IERC20-balanceOf}."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "decreaseAllowance(address,uint256)": {"details": "Atomically decreases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address. - `spender` must have allowance for the caller of at least `subtractedValue`."}, "increaseAllowance(address,uint256)": {"details": "Atomically increases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address."}, "name()": {"details": "Returns the name of the token."}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "See {IERC20-totalSupply}."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `amount`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `amount`. - the caller must have allowance for ``from``'s tokens of at least `amount`."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}, "storageLayout": {"storage": [{"astId": 138, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8"}, {"astId": 141, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool"}, {"astId": 1386, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage"}, {"astId": 319, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_balances", "offset": 0, "slot": "51", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 325, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_allowances", "offset": 0, "slot": "52", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))"}, {"astId": 327, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_totalSupply", "offset": 0, "slot": "53", "type": "t_uint256"}, {"astId": 329, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_name", "offset": 0, "slot": "54", "type": "t_string_storage"}, {"astId": 331, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_symbol", "offset": 0, "slot": "55", "type": "t_string_storage"}, {"astId": 911, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "56", "type": "t_array(t_uint256)45_storage"}, {"astId": 10, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_owner", "offset": 0, "slot": "101", "type": "t_address"}, {"astId": 130, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage"}, {"astId": 2131, "contract": "contracts/Netronlink.sol:Netronlink", "label": "vestingRecipient", "offset": 0, "slot": "151", "type": "t_address"}, {"astId": 2135, "contract": "contracts/Netronlink.sol:Netronlink", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "152", "type": "t_mapping(t_address,t_bool)"}, {"astId": 2145, "contract": "contracts/Netronlink.sol:Netronlink", "label": "teamVesting", "offset": 0, "slot": "153", "type": "t_struct(VestingSchedule)2142_storage"}, {"astId": 2557, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "156", "type": "t_array(t_uint256)45_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)45_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[45]", "numberOfBytes": "1440"}, "t_array(t_uint256)49_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_uint256)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(VestingSchedule)2142_storage": {"encoding": "inplace", "label": "struct Netronlink.VestingSchedule", "members": [{"astId": 2137, "contract": "contracts/Netronlink.sol:Netronlink", "label": "totalAllocated", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 2139, "contract": "contracts/Netronlink.sol:Netronlink", "label": "claimed", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 2141, "contract": "contracts/Netronlink.sol:Netronlink", "label": "startTime", "offset": 0, "slot": "2", "type": "t_uint64"}], "numberOfBytes": "96"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"encoding": "inplace", "label": "uint64", "numberOfBytes": "8"}, "t_uint8": {"encoding": "inplace", "label": "uint8", "numberOfBytes": "1"}}}}