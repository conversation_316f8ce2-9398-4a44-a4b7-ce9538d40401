{"address": "0xE2B13F52c846AA745bF3cE386a1B9754B4366709", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "presaleWallet", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "PresaleTokensClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TeamTokensClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensBurned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "WhitelistRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "Whitelisted", "type": "event"}, {"inputs": [], "name": "CLIFF", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MONTHLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_MONTHS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimTeamTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getClaimableTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "address", "name": "teamAndFounder", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "teamVesting", "outputs": [{"internalType": "uint256", "name": "totalAllocated", "type": "uint256"}, {"internalType": "uint256", "name": "claimed", "type": "uint256"}, {"internalType": "uint64", "name": "startTime", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "vestingRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "transactionHash": "0xfe0cb358ea9ee67559c2c37e8d19b42a8ed9d4341cd39abe6b8d993fc09ca6b1", "receipt": {"to": null, "from": "0xF96d2A2BDEdaFa702104e2386Af78FbECE04CEB3", "contractAddress": "0xE2B13F52c846AA745bF3cE386a1B9754B4366709", "transactionIndex": 2, "gasUsed": "2841682", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x7525c1f07d175de13eeb664794da50fe5e098b2ae36340c047529f02cb1920e5", "transactionHash": "0xfe0cb358ea9ee67559c2c37e8d19b42a8ed9d4341cd39abe6b8d993fc09ca6b1", "logs": [], "blockNumber": 55777256, "cumulativeGasUsed": "3307887", "status": 1, "byzantium": true}, "args": [], "numDeployments": 1, "solcInputHash": "01c12e656b59ed0097a08beca34f3860", "metadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"version\",\"type\":\"uint8\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"presaleWallet\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"PresaleTokensClaimed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TeamTokensClaimed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TokensBurned\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"WhitelistRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Whitelisted\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"CLIFF\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_SUPPLY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MONTHLY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"TOTAL_MONTHS\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"addWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"claimTeamTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"subtractedValue\",\"type\":\"uint256\"}],\"name\":\"decreaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getClaimableTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"addedValue\",\"type\":\"uint256\"}],\"name\":\"increaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"recipients\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"address\",\"name\":\"teamAndFounder\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"removeWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"teamVesting\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalAllocated\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"claimed\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"startTime\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"version\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vestingRecipient\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Initialized(uint8)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"See {IERC20-allowance}.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"See {IERC20-balanceOf}.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"decreaseAllowance(address,uint256)\":{\"details\":\"Atomically decreases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address. - `spender` must have allowance for the caller of at least `subtractedValue`.\"},\"increaseAllowance(address,uint256)\":{\"details\":\"Atomically increases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"See {IERC20-totalSupply}.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `amount`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `amount`. - the caller must have allowance for ``from``'s tokens of at least `amount`.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/Netronlink.sol\":\"Netronlink\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../utils/ContextUpgradeable.sol\\\";\\nimport \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * By default, the owner account will be the one that deploys the contract. This\\n * can later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract OwnableUpgradeable is Initializable, ContextUpgradeable {\\n    address private _owner;\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial owner.\\n     */\\n    function __Ownable_init() internal onlyInitializing {\\n        __Ownable_init_unchained();\\n    }\\n\\n    function __Ownable_init_unchained() internal onlyInitializing {\\n        _transferOwnership(_msgSender());\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        return _owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        require(owner() == _msgSender(), \\\"Ownable: caller is not the owner\\\");\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        require(newOwner != address(0), \\\"Ownable: new owner is the zero address\\\");\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        address oldOwner = _owner;\\n        _owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[49] private __gap;\\n}\\n\",\"keccak256\":\"0x4075622496acc77fd6d4de4cc30a8577a744d5c75afad33fdeacf1704d6eda98\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (proxy/utils/Initializable.sol)\\n\\npragma solidity ^0.8.2;\\n\\nimport \\\"../../utils/AddressUpgradeable.sol\\\";\\n\\n/**\\n * @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\\n * behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\\n * external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\\n * function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\\n *\\n * The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\\n * reused. This mechanism prevents re-execution of each \\\"step\\\" but allows the creation of new initialization steps in\\n * case an upgrade adds a module that needs to be initialized.\\n *\\n * For example:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```solidity\\n * contract MyToken is ERC20Upgradeable {\\n *     function initialize() initializer public {\\n *         __ERC20_init(\\\"MyToken\\\", \\\"MTK\\\");\\n *     }\\n * }\\n *\\n * contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\\n *     function initializeV2() reinitializer(2) public {\\n *         __ERC20Permit_init(\\\"MyToken\\\");\\n *     }\\n * }\\n * ```\\n *\\n * TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\\n * possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\\n *\\n * CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\\n * that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\\n *\\n * [CAUTION]\\n * ====\\n * Avoid leaving a contract uninitialized.\\n *\\n * An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\\n * contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\\n * the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```\\n * /// @custom:oz-upgrades-unsafe-allow constructor\\n * constructor() {\\n *     _disableInitializers();\\n * }\\n * ```\\n * ====\\n */\\nabstract contract Initializable {\\n    /**\\n     * @dev Indicates that the contract has been initialized.\\n     * @custom:oz-retyped-from bool\\n     */\\n    uint8 private _initialized;\\n\\n    /**\\n     * @dev Indicates that the contract is in the process of being initialized.\\n     */\\n    bool private _initializing;\\n\\n    /**\\n     * @dev Triggered when the contract has been initialized or reinitialized.\\n     */\\n    event Initialized(uint8 version);\\n\\n    /**\\n     * @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\\n     * `onlyInitializing` functions can be used to initialize parent contracts.\\n     *\\n     * Similar to `reinitializer(1)`, except that functions marked with `initializer` can be nested in the context of a\\n     * constructor.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier initializer() {\\n        bool isTopLevelCall = !_initializing;\\n        require(\\n            (isTopLevelCall && _initialized < 1) || (!AddressUpgradeable.isContract(address(this)) && _initialized == 1),\\n            \\\"Initializable: contract is already initialized\\\"\\n        );\\n        _initialized = 1;\\n        if (isTopLevelCall) {\\n            _initializing = true;\\n        }\\n        _;\\n        if (isTopLevelCall) {\\n            _initializing = false;\\n            emit Initialized(1);\\n        }\\n    }\\n\\n    /**\\n     * @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\\n     * contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\\n     * used to initialize parent contracts.\\n     *\\n     * A reinitializer may be used after the original initialization step. This is essential to configure modules that\\n     * are added through upgrades and that require initialization.\\n     *\\n     * When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\\n     * cannot be nested. If one is invoked in the context of another, execution will revert.\\n     *\\n     * Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\\n     * a contract, executing them in the right order is up to the developer or operator.\\n     *\\n     * WARNING: setting the version to 255 will prevent any future reinitialization.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier reinitializer(uint8 version) {\\n        require(!_initializing && _initialized < version, \\\"Initializable: contract is already initialized\\\");\\n        _initialized = version;\\n        _initializing = true;\\n        _;\\n        _initializing = false;\\n        emit Initialized(version);\\n    }\\n\\n    /**\\n     * @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\\n     * {initializer} and {reinitializer} modifiers, directly or indirectly.\\n     */\\n    modifier onlyInitializing() {\\n        require(_initializing, \\\"Initializable: contract is not initializing\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\\n     * Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\\n     * to any version. It is recommended to use this to lock implementation contracts that are designed to be called\\n     * through proxies.\\n     *\\n     * Emits an {Initialized} event the first time it is successfully executed.\\n     */\\n    function _disableInitializers() internal virtual {\\n        require(!_initializing, \\\"Initializable: contract is initializing\\\");\\n        if (_initialized != type(uint8).max) {\\n            _initialized = type(uint8).max;\\n            emit Initialized(type(uint8).max);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the highest version that has been initialized. See {reinitializer}.\\n     */\\n    function _getInitializedVersion() internal view returns (uint8) {\\n        return _initialized;\\n    }\\n\\n    /**\\n     * @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}.\\n     */\\n    function _isInitializing() internal view returns (bool) {\\n        return _initializing;\\n    }\\n}\\n\",\"keccak256\":\"0x89be10e757d242e9b18d5a32c9fbe2019f6d63052bbe46397a430a1d60d7f794\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (token/ERC20/ERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC20Upgradeable.sol\\\";\\nimport \\\"./extensions/IERC20MetadataUpgradeable.sol\\\";\\nimport \\\"../../utils/ContextUpgradeable.sol\\\";\\nimport \\\"../../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC20} interface.\\n *\\n * This implementation is agnostic to the way tokens are created. This means\\n * that a supply mechanism has to be added in a derived contract using {_mint}.\\n * For a generic mechanism see {ERC20PresetMinterPauser}.\\n *\\n * TIP: For a detailed writeup see our guide\\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\\n * to implement supply mechanisms].\\n *\\n * The default value of {decimals} is 18. To change this, you should override\\n * this function so it returns a different value.\\n *\\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\\n * instead returning `false` on failure. This behavior is nonetheless\\n * conventional and does not conflict with the expectations of ERC20\\n * applications.\\n *\\n * Additionally, an {Approval} event is emitted on calls to {transferFrom}.\\n * This allows applications to reconstruct the allowance for all accounts just\\n * by listening to said events. Other implementations of the EIP may not emit\\n * these events, as it isn't required by the specification.\\n *\\n * Finally, the non-standard {decreaseAllowance} and {increaseAllowance}\\n * functions have been added to mitigate the well-known issues around setting\\n * allowances. See {IERC20-approve}.\\n */\\ncontract ERC20Upgradeable is Initializable, ContextUpgradeable, IERC20Upgradeable, IERC20MetadataUpgradeable {\\n    mapping(address => uint256) private _balances;\\n\\n    mapping(address => mapping(address => uint256)) private _allowances;\\n\\n    uint256 private _totalSupply;\\n\\n    string private _name;\\n    string private _symbol;\\n\\n    /**\\n     * @dev Sets the values for {name} and {symbol}.\\n     *\\n     * All two of these values are immutable: they can only be set once during\\n     * construction.\\n     */\\n    function __ERC20_init(string memory name_, string memory symbol_) internal onlyInitializing {\\n        __ERC20_init_unchained(name_, symbol_);\\n    }\\n\\n    function __ERC20_init_unchained(string memory name_, string memory symbol_) internal onlyInitializing {\\n        _name = name_;\\n        _symbol = symbol_;\\n    }\\n\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() public view virtual override returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev Returns the symbol of the token, usually a shorter version of the\\n     * name.\\n     */\\n    function symbol() public view virtual override returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev Returns the number of decimals used to get its user representation.\\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n     * be displayed to a user as `5.05` (`505 / 10 ** 2`).\\n     *\\n     * Tokens usually opt for a value of 18, imitating the relationship between\\n     * Ether and Wei. This is the default value returned by this function, unless\\n     * it's overridden.\\n     *\\n     * NOTE: This information is only used for _display_ purposes: it in\\n     * no way affects any of the arithmetic of the contract, including\\n     * {IERC20-balanceOf} and {IERC20-transfer}.\\n     */\\n    function decimals() public view virtual override returns (uint8) {\\n        return 18;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-totalSupply}.\\n     */\\n    function totalSupply() public view virtual override returns (uint256) {\\n        return _totalSupply;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-balanceOf}.\\n     */\\n    function balanceOf(address account) public view virtual override returns (uint256) {\\n        return _balances[account];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transfer}.\\n     *\\n     * Requirements:\\n     *\\n     * - `to` cannot be the zero address.\\n     * - the caller must have a balance of at least `amount`.\\n     */\\n    function transfer(address to, uint256 amount) public virtual override returns (bool) {\\n        address owner = _msgSender();\\n        _transfer(owner, to, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-allowance}.\\n     */\\n    function allowance(address owner, address spender) public view virtual override returns (uint256) {\\n        return _allowances[owner][spender];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-approve}.\\n     *\\n     * NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on\\n     * `transferFrom`. This is semantically equivalent to an infinite approval.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     */\\n    function approve(address spender, uint256 amount) public virtual override returns (bool) {\\n        address owner = _msgSender();\\n        _approve(owner, spender, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transferFrom}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance. This is not\\n     * required by the EIP. See the note at the beginning of {ERC20}.\\n     *\\n     * NOTE: Does not update the allowance if the current allowance\\n     * is the maximum `uint256`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` and `to` cannot be the zero address.\\n     * - `from` must have a balance of at least `amount`.\\n     * - the caller must have allowance for ``from``'s tokens of at least\\n     * `amount`.\\n     */\\n    function transferFrom(address from, address to, uint256 amount) public virtual override returns (bool) {\\n        address spender = _msgSender();\\n        _spendAllowance(from, spender, amount);\\n        _transfer(from, to, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Atomically increases the allowance granted to `spender` by the caller.\\n     *\\n     * This is an alternative to {approve} that can be used as a mitigation for\\n     * problems described in {IERC20-approve}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     */\\n    function increaseAllowance(address spender, uint256 addedValue) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        _approve(owner, spender, allowance(owner, spender) + addedValue);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Atomically decreases the allowance granted to `spender` by the caller.\\n     *\\n     * This is an alternative to {approve} that can be used as a mitigation for\\n     * problems described in {IERC20-approve}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     * - `spender` must have allowance for the caller of at least\\n     * `subtractedValue`.\\n     */\\n    function decreaseAllowance(address spender, uint256 subtractedValue) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        uint256 currentAllowance = allowance(owner, spender);\\n        require(currentAllowance >= subtractedValue, \\\"ERC20: decreased allowance below zero\\\");\\n        unchecked {\\n            _approve(owner, spender, currentAllowance - subtractedValue);\\n        }\\n\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Moves `amount` of tokens from `from` to `to`.\\n     *\\n     * This internal function is equivalent to {transfer}, and can be used to\\n     * e.g. implement automatic token fees, slashing mechanisms, etc.\\n     *\\n     * Emits a {Transfer} event.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `from` must have a balance of at least `amount`.\\n     */\\n    function _transfer(address from, address to, uint256 amount) internal virtual {\\n        require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n        require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n        _beforeTokenTransfer(from, to, amount);\\n\\n        uint256 fromBalance = _balances[from];\\n        require(fromBalance >= amount, \\\"ERC20: transfer amount exceeds balance\\\");\\n        unchecked {\\n            _balances[from] = fromBalance - amount;\\n            // Overflow not possible: the sum of all balances is capped by totalSupply, and the sum is preserved by\\n            // decrementing then incrementing.\\n            _balances[to] += amount;\\n        }\\n\\n        emit Transfer(from, to, amount);\\n\\n        _afterTokenTransfer(from, to, amount);\\n    }\\n\\n    /** @dev Creates `amount` tokens and assigns them to `account`, increasing\\n     * the total supply.\\n     *\\n     * Emits a {Transfer} event with `from` set to the zero address.\\n     *\\n     * Requirements:\\n     *\\n     * - `account` cannot be the zero address.\\n     */\\n    function _mint(address account, uint256 amount) internal virtual {\\n        require(account != address(0), \\\"ERC20: mint to the zero address\\\");\\n\\n        _beforeTokenTransfer(address(0), account, amount);\\n\\n        _totalSupply += amount;\\n        unchecked {\\n            // Overflow not possible: balance + amount is at most totalSupply + amount, which is checked above.\\n            _balances[account] += amount;\\n        }\\n        emit Transfer(address(0), account, amount);\\n\\n        _afterTokenTransfer(address(0), account, amount);\\n    }\\n\\n    /**\\n     * @dev Destroys `amount` tokens from `account`, reducing the\\n     * total supply.\\n     *\\n     * Emits a {Transfer} event with `to` set to the zero address.\\n     *\\n     * Requirements:\\n     *\\n     * - `account` cannot be the zero address.\\n     * - `account` must have at least `amount` tokens.\\n     */\\n    function _burn(address account, uint256 amount) internal virtual {\\n        require(account != address(0), \\\"ERC20: burn from the zero address\\\");\\n\\n        _beforeTokenTransfer(account, address(0), amount);\\n\\n        uint256 accountBalance = _balances[account];\\n        require(accountBalance >= amount, \\\"ERC20: burn amount exceeds balance\\\");\\n        unchecked {\\n            _balances[account] = accountBalance - amount;\\n            // Overflow not possible: amount <= accountBalance <= totalSupply.\\n            _totalSupply -= amount;\\n        }\\n\\n        emit Transfer(account, address(0), amount);\\n\\n        _afterTokenTransfer(account, address(0), amount);\\n    }\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the `owner` s tokens.\\n     *\\n     * This internal function is equivalent to `approve`, and can be used to\\n     * e.g. set automatic allowances for certain subsystems, etc.\\n     *\\n     * Emits an {Approval} event.\\n     *\\n     * Requirements:\\n     *\\n     * - `owner` cannot be the zero address.\\n     * - `spender` cannot be the zero address.\\n     */\\n    function _approve(address owner, address spender, uint256 amount) internal virtual {\\n        require(owner != address(0), \\\"ERC20: approve from the zero address\\\");\\n        require(spender != address(0), \\\"ERC20: approve to the zero address\\\");\\n\\n        _allowances[owner][spender] = amount;\\n        emit Approval(owner, spender, amount);\\n    }\\n\\n    /**\\n     * @dev Updates `owner` s allowance for `spender` based on spent `amount`.\\n     *\\n     * Does not update the allowance amount in case of infinite allowance.\\n     * Revert if not enough allowance is available.\\n     *\\n     * Might emit an {Approval} event.\\n     */\\n    function _spendAllowance(address owner, address spender, uint256 amount) internal virtual {\\n        uint256 currentAllowance = allowance(owner, spender);\\n        if (currentAllowance != type(uint256).max) {\\n            require(currentAllowance >= amount, \\\"ERC20: insufficient allowance\\\");\\n            unchecked {\\n                _approve(owner, spender, currentAllowance - amount);\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Hook that is called before any transfer of tokens. This includes\\n     * minting and burning.\\n     *\\n     * Calling conditions:\\n     *\\n     * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n     * will be transferred to `to`.\\n     * - when `from` is zero, `amount` tokens will be minted for `to`.\\n     * - when `to` is zero, `amount` of ``from``'s tokens will be burned.\\n     * - `from` and `to` are never both zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _beforeTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n    /**\\n     * @dev Hook that is called after any transfer of tokens. This includes\\n     * minting and burning.\\n     *\\n     * Calling conditions:\\n     *\\n     * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n     * has been transferred to `to`.\\n     * - when `from` is zero, `amount` tokens have been minted for `to`.\\n     * - when `to` is zero, `amount` of ``from``'s tokens have been burned.\\n     * - `from` and `to` are never both zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _afterTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[45] private __gap;\\n}\\n\",\"keccak256\":\"0xd14a627157b9a411d2410713e5dd3a377e9064bd5c194a90748bbf27ea625784\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20Upgradeable {\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n    /**\\n     * @dev Returns the amount of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the amount of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves `amount` tokens from the caller's account to `to`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address to, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Moves `amount` tokens from `from` to `to` using the\\n     * allowance mechanism. `amount` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\",\"keccak256\":\"0x0e1f0f5f62f67a881cd1a9597acbc0a5e4071f3c2c10449a183b922ae7272e3f\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/extensions/IERC20MetadataUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20Upgradeable.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC20 standard.\\n *\\n * _Available since v4.1._\\n */\\ninterface IERC20MetadataUpgradeable is IERC20Upgradeable {\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the symbol of the token.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the decimals places of the token.\\n     */\\n    function decimals() external view returns (uint8);\\n}\\n\",\"keccak256\":\"0x605434219ebbe4653f703640f06969faa5a1d78f0bfef878e5ddbb1ca369ceeb\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (utils/Address.sol)\\n\\npragma solidity ^0.8.1;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary AddressUpgradeable {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following\\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     *\\n     * Furthermore, `isContract` will also return true if the target contract within\\n     * the same transaction is already scheduled for destruction by `SELFDESTRUCT`,\\n     * which only has an effect at the end of a transaction.\\n     * ====\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * You shouldn't rely on `isContract` to protect against flash loan attacks!\\n     *\\n     * Preventing calls from contracts is highly discouraged. It breaks composability, breaks support for smart wallets\\n     * like Gnosis Safe, and does not provide security since it can be circumvented by calling from a contract\\n     * constructor.\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // This method relies on extcodesize/address.code.length, which returns 0\\n        // for contracts in construction, since the code is only stored at the end\\n        // of the constructor execution.\\n\\n        return account.code.length > 0;\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://consensys.net/diligence/blog/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.8.0/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        (bool success, ) = recipient.call{value: amount}(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n\\n    /**\\n     * @dev Performs a Solidity function call using a low level `call`. A\\n     * plain `call` is an unsafe replacement for a function call: use this\\n     * function instead.\\n     *\\n     * If `target` reverts with a revert reason, it is bubbled up by this\\n     * function (like regular Solidity function calls).\\n     *\\n     * Returns the raw returned data. To convert to the expected return value,\\n     * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].\\n     *\\n     * Requirements:\\n     *\\n     * - `target` must be a contract.\\n     * - calling `target` with `data` must not revert.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, \\\"Address: low-level call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with\\n     * `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but also transferring `value` wei to `target`.\\n     *\\n     * Requirements:\\n     *\\n     * - the calling contract must have an ETH balance of at least `value`.\\n     * - the called Solidity function must be `payable`.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(address target, bytes memory data, uint256 value) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, value, \\\"Address: low-level call with value failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but\\n     * with `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(\\n        address target,\\n        bytes memory data,\\n        uint256 value,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        require(address(this).balance >= value, \\\"Address: insufficient balance for call\\\");\\n        (bool success, bytes memory returndata) = target.call{value: value}(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {\\n        return functionStaticCall(target, data, \\\"Address: low-level static call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.staticcall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionDelegateCall(target, data, \\\"Address: low-level delegate call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.delegatecall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call to smart-contract was successful, and revert (either by bubbling\\n     * the revert reason or using the provided one) in case of unsuccessful call or if target was not a contract.\\n     *\\n     * _Available since v4.8._\\n     */\\n    function verifyCallResultFromTarget(\\n        address target,\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        if (success) {\\n            if (returndata.length == 0) {\\n                // only check isContract if the call was successful and the return data is empty\\n                // otherwise we already know that it was a contract\\n                require(isContract(target), \\\"Address: call to non-contract\\\");\\n            }\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call was successful, and revert if it wasn't, either by bubbling the\\n     * revert reason or using the provided one.\\n     *\\n     * _Available since v4.3._\\n     */\\n    function verifyCallResult(\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal pure returns (bytes memory) {\\n        if (success) {\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    function _revert(bytes memory returndata, string memory errorMessage) private pure {\\n        // Look for revert reason and bubble it up if present\\n        if (returndata.length > 0) {\\n            // The easiest way to bubble the revert reason is using memory via assembly\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                let returndata_size := mload(returndata)\\n                revert(add(32, returndata), returndata_size)\\n            }\\n        } else {\\n            revert(errorMessage);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x9c80f545915582e63fe206c6ce27cbe85a86fc10b9cd2a0e8c9488fb7c2ee422\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\nimport \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract ContextUpgradeable is Initializable {\\n    function __Context_init() internal onlyInitializing {\\n    }\\n\\n    function __Context_init_unchained() internal onlyInitializing {\\n    }\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[50] private __gap;\\n}\\n\",\"keccak256\":\"0x963ea7f0b48b032eef72fe3a7582edf78408d6f834115b9feadd673a4d5bd149\",\"license\":\"MIT\"},\"contracts/Netronlink.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.28;\\n// final\\n\\nimport \\\"@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\\\";\\n\\ncontract Netronlink is Initializable, ERC20Upgradeable, OwnableUpgradeable {\\n    uint256 public constant MAX_SUPPLY = 80000000 * 1e18;\\n    uint256 public constant CLIFF = 2 minutes;\\n    uint256 public constant MONTHLY = 2 minutes;\\n    uint256 public constant TOTAL_MONTHS = 1;\\n    uint256 internal constant teamAmount = ******** * 1e18;\\n    address public vestingRecipient;\\n\\n    mapping(address => bool) isWhitelisted;\\n\\n    //Struct\\n    struct VestingSchedule {\\n        uint256 totalAllocated;\\n        uint256 claimed;\\n        uint64 startTime;\\n    }\\n    VestingSchedule public teamVesting;\\n\\n    //EVENTS--\\n    event Whitelisted(address indexed account);\\n    event WhitelistRemoved(address indexed account);\\n    event TeamTokensClaimed(address indexed recipient, uint256 amount);\\n    event TokensBurned(address indexed from, uint256 amount);\\n    event PresaleTokensClaimed(address presaleWallet, uint256 amount); \\n\\n    function initialize(\\n        address[] memory recipients,\\n        uint256[] memory amounts,\\n        address teamAndFounder\\n    ) public initializer {\\n        __ERC20_init(\\\"Netronlink Token\\\", \\\"NTL\\\");\\n        __Ownable_init();\\n\\n        require(recipients.length == amounts.length, \\\"Mismatched input arrays\\\");\\n\\n        uint256 totalToMint = 0;\\n        for (uint256 i = 0; i < amounts.length; i++) {\\n            totalToMint += amounts[i];\\n        }\\n\\n        require(totalToMint <= MAX_SUPPLY, \\\"Exceeds max supply\\\");\\n\\n        for (uint256 i = 0; i < recipients.length; i++) {\\n            _mint(recipients[i], amounts[i]);\\n        }\\n\\n        _mint(address(this), teamAmount);\\n\\n        teamVesting = VestingSchedule({\\n            totalAllocated: teamAmount,\\n            claimed: 0,\\n            startTime: uint64(1750662600)\\n        });\\n        vestingRecipient = teamAndFounder;\\n        addWhitelist(address(this));\\n    }\\n\\n    function _transfer(address from, address to, uint256 amount) internal override {\\n        require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n        require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n        uint256 burnAmount = 0;\\n        uint256 sendAmount = amount;\\n\\n        if (!isWhitelisted[from] && !isWhitelisted[to]) {\\n            burnAmount = amount / 100; // 1%\\n            sendAmount = amount - burnAmount;\\n        }\\n\\n        // If there's a burn, handle it first by reducing the from balance\\n        if (burnAmount > 0) {\\n            _burn(from, burnAmount);\\n            emit TokensBurned(from, burnAmount); // burn log\\n        }\\n        \\n        // Call parent _transfer for the send amount\\n        super._transfer(from, to, sendAmount);\\n    }\\n\\n    function addWhitelist(address user) public onlyOwner {\\n        isWhitelisted[user] = true;\\n        emit Whitelisted(user);\\n    }\\n\\n    function removeWhitelist(address user) public onlyOwner {\\n        isWhitelisted[user] = false;\\n        emit WhitelistRemoved(user);\\n    }\\n\\n    function claimTeamTokens() external {\\n        require(msg.sender == vestingRecipient, \\\"Not authorized\\\");\\n\\n        uint256 claimable = _calculateClaimable();\\n        require(claimable > 0, \\\"Nothing to claim\\\");\\n\\n        teamVesting.claimed += claimable;\\n        _transfer(address(this), msg.sender, claimable);\\n\\n        emit TeamTokensClaimed(msg.sender, claimable);\\n    }\\n\\n    function _calculateClaimable() internal view returns (uint256 claimable) {\\n        VestingSchedule storage vest = teamVesting;\\n\\n        if (block.timestamp < vest.startTime + CLIFF) {\\n            return 0; // still in cliff\\n        }\\n\\n        uint256 monthsElapsed = (block.timestamp - vest.startTime - CLIFF) / MONTHLY;\\n        if (monthsElapsed > TOTAL_MONTHS) monthsElapsed = TOTAL_MONTHS;\\n\\n        uint256 totalVested = (vest.totalAllocated * monthsElapsed) / TOTAL_MONTHS;\\n        if (monthsElapsed == TOTAL_MONTHS) {\\n            totalVested = vest.totalAllocated;\\n        }\\n\\n        if (totalVested <= vest.claimed) {\\n            return 0;\\n        }\\n\\n        return totalVested - vest.claimed;\\n    }\\n\\n    function getClaimableTokens() external view returns (uint256) { \\n        require(msg.sender == vestingRecipient, \\\"Not authorized\\\");\\n        return _calculateClaimable();\\n    }\\n\\n    // Version function for upgrade tracking\\n    function version() external pure returns (string memory) {\\n        return \\\"1.0.0\\\";\\n    }\\n\\n    // Gap for future storage variables in upgrades\\n    uint256[45] private __gap;\\n}\\n\",\"keccak256\":\"0x9cca2c0770724083178f961ac687861d5c9f9064e0b642cbdc7f0f1f6281761f\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"events": {"Approval(address,address,uint256)": {"details": "Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance."}, "Initialized(uint8)": {"details": "Triggered when the contract has been initialized or reinitialized."}, "Transfer(address,address,uint256)": {"details": "Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero."}}, "kind": "dev", "methods": {"allowance(address,address)": {"details": "See {IERC20-allowance}."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "See {IERC20-balanceOf}."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "decreaseAllowance(address,uint256)": {"details": "Atomically decreases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address. - `spender` must have allowance for the caller of at least `subtractedValue`."}, "increaseAllowance(address,uint256)": {"details": "Atomically increases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address."}, "name()": {"details": "Returns the name of the token."}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "See {IERC20-totalSupply}."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `amount`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `amount`. - the caller must have allowance for ``from``'s tokens of at least `amount`."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}, "storageLayout": {"storage": [{"astId": 138, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8"}, {"astId": 141, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool"}, {"astId": 1386, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage"}, {"astId": 319, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_balances", "offset": 0, "slot": "51", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 325, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_allowances", "offset": 0, "slot": "52", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))"}, {"astId": 327, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_totalSupply", "offset": 0, "slot": "53", "type": "t_uint256"}, {"astId": 329, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_name", "offset": 0, "slot": "54", "type": "t_string_storage"}, {"astId": 331, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_symbol", "offset": 0, "slot": "55", "type": "t_string_storage"}, {"astId": 911, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "56", "type": "t_array(t_uint256)45_storage"}, {"astId": 10, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_owner", "offset": 0, "slot": "101", "type": "t_address"}, {"astId": 130, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage"}, {"astId": 1419, "contract": "contracts/Netronlink.sol:Netronlink", "label": "vestingRecipient", "offset": 0, "slot": "151", "type": "t_address"}, {"astId": 1423, "contract": "contracts/Netronlink.sol:Netronlink", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "152", "type": "t_mapping(t_address,t_bool)"}, {"astId": 1433, "contract": "contracts/Netronlink.sol:Netronlink", "label": "teamVesting", "offset": 0, "slot": "153", "type": "t_struct(VestingSchedule)1430_storage"}, {"astId": 1844, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "156", "type": "t_array(t_uint256)45_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)45_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[45]", "numberOfBytes": "1440"}, "t_array(t_uint256)49_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_uint256)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(VestingSchedule)1430_storage": {"encoding": "inplace", "label": "struct Netronlink.VestingSchedule", "members": [{"astId": 1425, "contract": "contracts/Netronlink.sol:Netronlink", "label": "totalAllocated", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 1427, "contract": "contracts/Netronlink.sol:Netronlink", "label": "claimed", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 1429, "contract": "contracts/Netronlink.sol:Netronlink", "label": "startTime", "offset": 0, "slot": "2", "type": "t_uint64"}], "numberOfBytes": "96"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"encoding": "inplace", "label": "uint64", "numberOfBytes": "8"}, "t_uint8": {"encoding": "inplace", "label": "uint8", "numberOfBytes": "1"}}}}