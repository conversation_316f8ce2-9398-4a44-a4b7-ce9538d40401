<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      /
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">61.9% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>39/63</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">57.41% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>31/54</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">36.36% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>8/22</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">59.26% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>48/81</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="contracts/"><a href="contracts/index.html">contracts/</a></td>
	<td data-value="61.9" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 61%;"></div><div class="cover-empty" style="width:39%;"></div></div></td>
	<td data-value="61.9" class="pct medium">61.9%</td>
	<td data-value="63" class="abs medium">39/63</td>
	<td data-value="57.41" class="pct medium">57.41%</td>
	<td data-value="54" class="abs medium">31/54</td>
	<td data-value="36.36" class="pct low">36.36%</td>
	<td data-value="22" class="abs low">8/22</td>
	<td data-value="59.26" class="pct medium">59.26%</td>
	<td data-value="81" class="abs medium">48/81</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Thu Jun 19 2025 16:43:35 GMT+0530 (India Standard Time)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
