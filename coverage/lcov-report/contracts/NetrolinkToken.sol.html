<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/NetrolinkToken.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> NetrolinkToken.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">96.67% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>29/30</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">46.43% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>13/28</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">83.33% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>5/6</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">88.1% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>37/42</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;
&nbsp;
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
&nbsp;
contract Netrolink is ERC20, Ownable {
    uint256 public constant MAX_SUPPLY = 80000000 * 1e18; // 100 million with 18
    mapping(address =&gt; bool) isWhitelisted;
    uint256 public constant CLIFF = 365 days; // 12 months
    uint256 public constant MONTHLY = 30 days; // 1 month duration
    uint256 public constant TOTAL_MONTHS = 24; // vesting period in months
    uint256 internal constant teamAmount = 20000000 * 1e18;
&nbsp;
    address public vestingRecipient;
&nbsp;
    // Vesting schedule for team+founder
    struct VestingSchedule {
        uint256 totalAllocated;
        uint256 claimed;
        uint64 startTime;
    }
&nbsp;
    VestingSchedule public teamVesting;
&nbsp;
    constructor(
        address[] memory recipients,
        uint256[] memory amounts,
        address teamAndFounder
    ) ERC20("Netrolink Token", "NNET") {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(recipients.length == amounts.length, "Mismatched input arrays");
&nbsp;
        uint256 totalToMint = 0;
        for (uint256 i = 0; i &lt; amounts.length; i++) {
            totalToMint += amounts[i];
        }
&nbsp;
        <span class="missing-if-branch" title="else path not taken" >E</span>require(totalToMint &lt;= MAX_SUPPLY, "Exceeds max supply");
&nbsp;
        for (uint256 i = 0; i &lt; recipients.length; i++) {
            _mint(recipients[i], amounts[i]);
        }
&nbsp;
        _mint(address(this), teamAmount);
&nbsp;
        teamVesting = VestingSchedule({
            totalAllocated: teamAmount,
            claimed: 0,
            startTime: uint64(block.timestamp)
        });
        vestingRecipient = teamAndFounder;
        addWhitelist(address(this));
    }
&nbsp;
    function _transfer(address from, address to, uint256 amount) internal override {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(from != address(0), "ERC20: transfer from the zero address");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(to != address(0), "ERC20: transfer to the zero address");
&nbsp;
        uint256 fromBalance = _balances[from];
        <span class="missing-if-branch" title="else path not taken" >E</span>require(fromBalance &gt;= amount, "ERC20: transfer amount exceeds balance");
&nbsp;
        uint256 burnAmount = 0;
        uint256 sendAmount = amount;
&nbsp;
        // ✅ Apply burn only if sender is not whitelisted
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!isWhitelisted[from]) {
            burnAmount = amount / 100; // 1%
            sendAmount = amount - burnAmount;
        }
&nbsp;
        unchecked {
            _balances[from] = fromBalance - amount;
        }
        _balances[to] += sendAmount;
        emit Transfer(from, to, sendAmount);
        <span class="missing-if-branch" title="if path not taken" >I</span>if (burnAmount &gt; 0) {
            _balances[address(0)] += burnAmount;
<span class="cstat-no" title="statement not covered" >            emit Transfer(from, address(0), burnAmount);</span> // burn log
        }
    }
&nbsp;
    function addWhitelist(address user) public <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        isWhitelisted[user] = true;
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function removeWhitelist(address user) public onlyOwner {</span>
        isWhitelisted[user] = false;
    }
&nbsp;
    function claimTeamTokens() external {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(msg.sender == vestingRecipient, "Not authorized");
&nbsp;
        VestingSchedule storage vest = teamVesting;
        <span class="missing-if-branch" title="else path not taken" >E</span>require(vest.totalAllocated &gt; 0, "No vesting set");
&nbsp;
        uint256 elapsed = block.timestamp - vest.startTime;
        <span class="missing-if-branch" title="else path not taken" >E</span>require(elapsed &gt;= CLIFF, "Cliff not reached");
&nbsp;
        uint256 monthsElapsed = (elapsed - CLIFF) / MONTHLY;
        <span class="missing-if-branch" title="else path not taken" >E</span>if (monthsElapsed &gt; TOTAL_MONTHS) monthsElapsed = TOTAL_MONTHS;
&nbsp;
        uint256 totalVested = (vest.totalAllocated * monthsElapsed) / TOTAL_MONTHS;
        uint256 claimable = totalVested - vest.claimed;
        <span class="missing-if-branch" title="else path not taken" >E</span>require(claimable &gt; 0, "Nothing to claim");
&nbsp;
        vest.claimed += claimable;
        _transfer(address(this), msg.sender, claimable);
    }
&nbsp;
    function getMonth() public pure returns (uint256) {
        return 6000 days;
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Thu May 22 2025 10:01:49 GMT+0000 (Coordinated Universal Time)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
