<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/Netrolink.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> Netrolink.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">52.63% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>20/38</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">25% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>8/32</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">42.86% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>3/7</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">58.82% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>30/51</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;
&nbsp;
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
&nbsp;
contract Netrolink is ERC20, Ownable {
    uint256 public constant MAX_SUPPLY = 80000000 * 1e18; // 100 million with 18
    uint256 public constant CLIFF = 365 days; // 12 months
    uint256 public constant MONTHLY = 30 days; // 1 month duration
    uint256 public constant TOTAL_MONTHS = 24; // vesting period in months
    uint256 internal constant teamAmount = ******** * 1e18;
    address public vestingRecipient;
    mapping(address =&gt; bool) isWhitelisted;
&nbsp;
    //Struct
    struct VestingSchedule {
        uint256 totalAllocated;
        uint256 claimed;
        uint64 startTime;
    }
    VestingSchedule public teamVesting;
&nbsp;
    //EVENTS--
    event Whitelisted(address indexed account);
    event WhitelistRemoved(address indexed account);
    event TeamTokensClaimed(address indexed recipient, uint256 amount);
    event TokensBurned(address indexed from, uint256 amount);
&nbsp;
    constructor(
        address[] memory recipients,
        uint256[] memory amounts,
        address teamAndFounder
    ) ERC20("Netrolink Token", "NNET") {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(recipients.length == amounts.length, "Mismatched input arrays");
&nbsp;
        uint256 totalToMint = 0;
        for (uint256 i = 0; i &lt; amounts.length; i++) {
            totalToMint += amounts[i];
        }
&nbsp;
        <span class="missing-if-branch" title="else path not taken" >E</span>require(totalToMint &lt;= MAX_SUPPLY, "Exceeds max supply");
&nbsp;
        for (uint256 i = 0; i &lt; recipients.length; i++) {
            _mint(recipients[i], amounts[i]);
        }
&nbsp;
        _mint(address(this), teamAmount);
&nbsp;
        teamVesting = VestingSchedule({
            totalAllocated: teamAmount,
            claimed: 0,
            startTime: uint64(block.timestamp)
        });
        vestingRecipient = teamAndFounder;
        addWhitelist(address(this));
    }
&nbsp;
    function _transfer(address from, address to, uint256 amount) internal override {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(from != address(0), "ERC20: transfer from the zero address");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(to != address(0), "ERC20: transfer to the zero address");
&nbsp;
        uint256 fromBalance = _balances[from];
        <span class="missing-if-branch" title="else path not taken" >E</span>require(fromBalance &gt;= amount, "ERC20: transfer amount exceeds balance");
&nbsp;
        uint256 burnAmount = 0;
        uint256 sendAmount = amount;
&nbsp;
        <span class="missing-if-branch" title="else path not taken" >E</span>if (!isWhitelisted[from] &amp;&amp; !isWhitelisted[to]) {
            burnAmount = amount / 100; // 1%
            sendAmount = amount - burnAmount;
        }
&nbsp;
        unchecked {
            _balances[from] = fromBalance - amount;
        }
        _balances[to] += sendAmount;
        emit Transfer(from, to, sendAmount);
        <span class="missing-if-branch" title="else path not taken" >E</span>if (burnAmount &gt; 0) {
            _balances[address(0)] += burnAmount;
            emit Transfer(from, address(0), burnAmount);
            emit TokensBurned(from, burnAmount); // burn log
        }
    }
&nbsp;
    function addWhitelist(address user) public <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        isWhitelisted[user] = true;
        emit Whitelisted(user);
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function removeWhitelist(address user) public onlyOwner {</span>
        isWhitelisted[user] = false;
<span class="cstat-no" title="statement not covered" >        emit WhitelistRemoved(user);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function claimTeamTokens() external {</span>
<span class="cstat-no" title="statement not covered" >        require(msg.sender == vestingRecipient, "Not authorized")</span>;
&nbsp;
<span class="cstat-no" title="statement not covered" >        uint256 claimable = _calculateClaimable();</span>
<span class="cstat-no" title="statement not covered" >        require(claimable &gt; 0, "Nothing to claim")</span>;
&nbsp;
        teamVesting.claimed += claimable;
<span class="cstat-no" title="statement not covered" >        _transfer(address(this), msg.sender, claimable)</span>;
&nbsp;
<span class="cstat-no" title="statement not covered" >        emit TeamTokensClaimed(msg.sender, claimable);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function _calculateClaimable() internal view returns (uint256 claimable) {</span>
<span class="cstat-no" title="statement not covered" >        VestingSchedule storage vest = teamVesting;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (block.timestamp &lt; vest.startTime + CLIFF) {</span>
<span class="cstat-no" title="statement not covered" >            return 0;</span> // still in cliff
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        uint256 monthsElapsed = (block.timestamp - vest.startTime - CLIFF) / MONTHLY;</span>
<span class="cstat-no" title="statement not covered" >        if (monthsElapsed &gt; TOTAL_MONTHS) monthsElapsed = TOTAL_MONTHS;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        uint256 totalVested = (vest.totalAllocated * monthsElapsed) / TOTAL_MONTHS;</span>
<span class="cstat-no" title="statement not covered" >        if (monthsElapsed == TOTAL_MONTHS) {</span>
            totalVested = vest.totalAllocated;
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (totalVested &lt;= vest.claimed) {</span>
<span class="cstat-no" title="statement not covered" >            return 0;</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        return totalVested - vest.claimed;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function getClaimableTokens() external view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        require(msg.sender == vestingRecipient, "Not authorized")</span>;
<span class="cstat-no" title="statement not covered" >        return _calculateClaimable();</span>
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Mon May 26 2025 09:29:04 GMT+0000 (Coordinated Universal Time)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
