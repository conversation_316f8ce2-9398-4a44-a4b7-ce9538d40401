<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/Netronlink.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> Netronlink.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>39/39</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">96.88% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>31/32</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>8/8</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>48/48</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-yes">66×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19×</span>
<span class="cline-any cline-yes">19×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">47×</span>
<span class="cline-any cline-yes">47×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;
// final
&nbsp;
import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
&nbsp;
contract Netronlink is Initializable, ERC20Upgradeable, OwnableUpgradeable {
    uint256 public constant MAX_SUPPLY = 80000000 * 1e18;
    uint256 public constant CLIFF = 365 days;
    uint256 public constant MONTHLY = 30 days;
    uint256 public constant TOTAL_MONTHS = 24;
    uint256 internal constant teamAmount = ******** * 1e18;
    address public vestingRecipient;
&nbsp;
    mapping(address =&gt; bool) isWhitelisted;
&nbsp;
    //Struct
    struct VestingSchedule {
        uint256 totalAllocated;
        uint256 claimed;
        uint64 startTime;
    }
    VestingSchedule public teamVesting;
&nbsp;
    //EVENTS--
    event Whitelisted(address indexed account);
    event WhitelistRemoved(address indexed account);
    event TeamTokensClaimed(address indexed recipient, uint256 amount);
    event TokensBurned(address indexed from, uint256 amount);
    event PresaleTokensClaimed(address presaleWallet, uint256 amount); 
&nbsp;
    function initialize(
        address[] memory recipients,
        uint256[] memory amounts,
        address teamAndFounder
    ) public initializer {
        __ERC20_init("Netronlink Token", "NTL");
        __Ownable_init();
&nbsp;
        require(recipients.length == amounts.length, "Mismatched input arrays");
&nbsp;
        uint256 totalToMint = 0;
        for (uint256 i = 0; i &lt; amounts.length; i++) {
            totalToMint += amounts[i];
        }
&nbsp;
        require(totalToMint &lt;= MAX_SUPPLY, "Exceeds max supply");
&nbsp;
        for (uint256 i = 0; i &lt; recipients.length; i++) {
            _mint(recipients[i], amounts[i]);
        }
&nbsp;
        _mint(address(this), teamAmount);
&nbsp;
        teamVesting = VestingSchedule({
            totalAllocated: teamAmount,
            claimed: 0,
            startTime: uint64(block.timestamp)
        });
        vestingRecipient = teamAndFounder;
        addWhitelist(address(this));
    }
&nbsp;
    function _transfer(address from, address to, uint256 amount) internal override {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(from != address(0), "ERC20: transfer from the zero address");
        require(to != address(0), "ERC20: transfer to the zero address");
&nbsp;
        uint256 burnAmount = 0;
        uint256 sendAmount = amount;
&nbsp;
        if (!isWhitelisted[from] &amp;&amp; !isWhitelisted[to]) {
            burnAmount = amount / 100; // 1%
            sendAmount = amount - burnAmount;
        }
&nbsp;
        // If there's a burn, handle it first by reducing the from balance
        if (burnAmount &gt; 0) {
            _burn(from, burnAmount);
            emit TokensBurned(from, burnAmount); // burn log
        }
        
        // Call parent _transfer for the send amount
        super._transfer(from, to, sendAmount);
    }
&nbsp;
    function addWhitelist(address user) public onlyOwner {
        isWhitelisted[user] = true;
        emit Whitelisted(user);
    }
&nbsp;
    function removeWhitelist(address user) public onlyOwner {
        isWhitelisted[user] = false;
        emit WhitelistRemoved(user);
    }
&nbsp;
    function claimTeamTokens() external {
        require(msg.sender == vestingRecipient, "Not authorized");
&nbsp;
        uint256 claimable = _calculateClaimable();
        require(claimable &gt; 0, "Nothing to claim");
&nbsp;
        teamVesting.claimed += claimable;
        _transfer(address(this), msg.sender, claimable);
&nbsp;
        emit TeamTokensClaimed(msg.sender, claimable);
    }
&nbsp;
    function _calculateClaimable() internal view returns (uint256 claimable) {
        VestingSchedule storage vest = teamVesting;
&nbsp;
        if (block.timestamp &lt; vest.startTime + CLIFF) {
            return 0; // still in cliff
        }
&nbsp;
        uint256 monthsElapsed = (block.timestamp - vest.startTime - CLIFF) / MONTHLY;
        if (monthsElapsed &gt; TOTAL_MONTHS) monthsElapsed = TOTAL_MONTHS;
&nbsp;
        uint256 totalVested = (vest.totalAllocated * monthsElapsed) / TOTAL_MONTHS;
        if (monthsElapsed == TOTAL_MONTHS) {
            totalVested = vest.totalAllocated;
        }
&nbsp;
        if (totalVested &lt;= vest.claimed) {
            return 0;
        }
&nbsp;
        return totalVested - vest.claimed;
    }
&nbsp;
    function getClaimableTokens() external view returns (uint256) { 
        require(msg.sender == vestingRecipient, "Not authorized");
        return _calculateClaimable();
    }
&nbsp;
    // Version function for upgrade tracking
    function version() external pure returns (string memory) {
        return "1.0.0";
    }
&nbsp;
    // Gap for future storage variables in upgrades
    uint256[45] private __gap;
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Thu Jun 19 2025 16:43:35 GMT+0530 (India Standard Time)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
