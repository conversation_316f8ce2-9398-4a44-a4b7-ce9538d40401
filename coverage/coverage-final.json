{"contracts/Netronlink.sol": {"l": {"39": 35, "40": 35, "42": 35, "44": 34, "45": 34, "46": 34, "49": 34, "51": 33, "52": 66, "55": 33, "57": 33, "62": 33, "63": 33, "67": 19, "68": 19, "70": 18, "71": 18, "73": 18, "74": 7, "75": 7, "79": 18, "80": 6, "81": 6, "85": 18, "89": 47, "90": 47, "94": 2, "95": 2, "99": 9, "101": 8, "102": 8, "104": 6, "105": 6, "107": 6, "111": 12, "113": 12, "114": 2, "117": 10, "118": 10, "120": 10, "121": 10, "122": 3, "125": 10, "126": 1, "129": 9, "133": 5, "134": 4, "139": 3}, "path": "/data/PROJECTS/netronlink_sc-stable/contracts/Netronlink.sol", "s": {"1": 35, "2": 35, "3": 35, "4": 34, "5": 34, "6": 34, "7": 33, "8": 66, "9": 33, "10": 33, "11": 19, "12": 19, "13": 18, "14": 18, "15": 18, "16": 18, "17": 6, "18": 6, "19": 18, "20": 47, "21": 2, "22": 9, "23": 8, "24": 8, "25": 6, "26": 6, "27": 12, "28": 12, "29": 2, "30": 10, "31": 10, "32": 10, "33": 10, "34": 10, "35": 1, "36": 9, "37": 5, "38": 4, "39": 3}, "b": {"1": [35, 1], "2": [34, 1], "3": [33, 1], "4": [19, 0], "5": [18, 1], "6": [7, 11], "7": [6, 12], "8": [47, 1], "9": [2, 1], "10": [8, 1], "11": [6, 2], "12": [2, 10], "13": [1, 9], "14": [3, 7], "15": [1, 9], "16": [4, 1]}, "f": {"1": 35, "2": 19, "3": 47, "4": 2, "5": 9, "6": 12, "7": 5, "8": 3}, "fnMap": {"1": {"name": "initialize", "line": 38, "loc": {"start": {"line": 34, "column": 4}, "end": {"line": 64, "column": 4}}}, "2": {"name": "_transfer", "line": 66, "loc": {"start": {"line": 66, "column": 4}, "end": {"line": 86, "column": 4}}}, "3": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 88, "loc": {"start": {"line": 88, "column": 4}, "end": {"line": 91, "column": 4}}}, "4": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 93, "loc": {"start": {"line": 93, "column": 4}, "end": {"line": 96, "column": 4}}}, "5": {"name": "claimTeamTokens", "line": 98, "loc": {"start": {"line": 98, "column": 4}, "end": {"line": 108, "column": 4}}}, "6": {"name": "_calculateClaimable", "line": 110, "loc": {"start": {"line": 110, "column": 4}, "end": {"line": 130, "column": 4}}}, "7": {"name": "getClaimableTokens", "line": 132, "loc": {"start": {"line": 132, "column": 4}, "end": {"line": 135, "column": 4}}}, "8": {"name": "version", "line": 138, "loc": {"start": {"line": 138, "column": 4}, "end": {"line": 140, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 46}}, "2": {"start": {"line": 40, "column": 8}, "end": {"line": 40, "column": 23}}, "3": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 78}}, "4": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 31}}, "5": {"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": 1574}}, "6": {"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 63}}, "7": {"start": {"line": 51, "column": 8}, "end": {"line": 51, "column": 1746}}, "8": {"start": {"line": 52, "column": 12}, "end": {"line": 52, "column": 43}}, "9": {"start": {"line": 55, "column": 8}, "end": {"line": 55, "column": 39}}, "10": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 34}}, "11": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 75}}, "12": {"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": 71}}, "13": {"start": {"line": 70, "column": 8}, "end": {"line": 70, "column": 30}}, "14": {"start": {"line": 71, "column": 8}, "end": {"line": 71, "column": 35}}, "15": {"start": {"line": 73, "column": 8}, "end": {"line": 73, "column": 2462}}, "16": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 2697}}, "17": {"start": {"line": 80, "column": 12}, "end": {"line": 80, "column": 34}}, "18": {"start": {"line": 81, "column": 12}, "end": {"line": 81, "column": 47}}, "19": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 44}}, "20": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 30}}, "21": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 35}}, "22": {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 64}}, "23": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 49}}, "24": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 49}}, "25": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 54}}, "26": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 53}}, "27": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 50}}, "28": {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 3736}}, "29": {"start": {"line": 114, "column": 12}, "end": {"line": 114, "column": 20}}, "30": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 84}}, "31": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 70}}, "32": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 82}}, "33": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 4086}}, "34": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 4189}}, "35": {"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 20}}, "36": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 41}}, "37": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 64}}, "38": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 36}}, "39": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 22}}}, "branchMap": {"1": {"line": 38, "type": "if", "locations": [{"start": {"line": 38, "column": 13}, "end": {"line": 38, "column": 13}}, {"start": {"line": 38, "column": 13}, "end": {"line": 38, "column": 13}}]}, "2": {"line": 42, "type": "if", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 8}}, {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 8}}]}, "3": {"line": 49, "type": "if", "locations": [{"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 8}}, {"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 8}}]}, "4": {"line": 67, "type": "if", "locations": [{"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 8}}, {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 8}}]}, "5": {"line": 68, "type": "if", "locations": [{"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": 8}}, {"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": 8}}]}, "6": {"line": 73, "type": "if", "locations": [{"start": {"line": 73, "column": 8}, "end": {"line": 73, "column": 8}}, {"start": {"line": 73, "column": 8}, "end": {"line": 73, "column": 8}}]}, "7": {"line": 79, "type": "if", "locations": [{"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 8}}, {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 8}}]}, "8": {"line": 88, "type": "if", "locations": [{"start": {"line": 88, "column": 47}, "end": {"line": 88, "column": 47}}, {"start": {"line": 88, "column": 47}, "end": {"line": 88, "column": 47}}]}, "9": {"line": 93, "type": "if", "locations": [{"start": {"line": 93, "column": 50}, "end": {"line": 93, "column": 50}}, {"start": {"line": 93, "column": 50}, "end": {"line": 93, "column": 50}}]}, "10": {"line": 99, "type": "if", "locations": [{"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 8}}, {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 8}}]}, "11": {"line": 102, "type": "if", "locations": [{"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 8}}, {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 8}}]}, "12": {"line": 113, "type": "if", "locations": [{"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 8}}, {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 8}}]}, "13": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}, {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}]}, "14": {"line": 121, "type": "if", "locations": [{"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 8}}, {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 8}}]}, "15": {"line": 125, "type": "if", "locations": [{"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 8}}, {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 8}}]}, "16": {"line": 133, "type": "if", "locations": [{"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 8}}, {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 8}}]}}}, "contracts/OwnedUpgradeabilityProxy.sol": {"l": {"36": 0, "44": 0, "45": 0, "54": 0, "55": 0, "65": 0, "66": 0, "75": 0, "76": 0, "86": 0, "87": 0, "88": 0, "96": 0, "107": 0, "108": 0, "109": 0, "117": 0, "121": 0, "129": 0, "130": 0, "140": 0, "141": 0, "151": 0, "152": 0, "153": 0, "154": 0, "158": 0, "159": 0, "161": 0, "162": 0, "163": 0, "180": 0, "181": 0}, "path": "/data/PROJECTS/netronlink_sc-stable/contracts/OwnedUpgradeabilityProxy.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "fnMap": {"1": {"name": "constructor", "line": 35, "loc": {"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 4}}}, "2": {"name": "maintenance", "line": 43, "loc": {"start": {"line": 43, "column": 4}, "end": {"line": 48, "column": 4}}}, "3": {"name": "setMaintenance", "line": 53, "loc": {"start": {"line": 53, "column": 4}, "end": {"line": 58, "column": 4}}}, "4": {"name": "proxyOwner", "line": 64, "loc": {"start": {"line": 64, "column": 4}, "end": {"line": 69, "column": 4}}}, "5": {"name": "setUpgradeabilityOwner", "line": 74, "loc": {"start": {"line": 74, "column": 4}, "end": {"line": 79, "column": 4}}}, "6": {"name": "transferProxyOwnership", "line": 85, "loc": {"start": {"line": 85, "column": 4}, "end": {"line": 89, "column": 4}}}, "7": {"name": "upgradeTo", "line": 95, "loc": {"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 4}}}, "8": {"name": "upgradeToAndCall", "line": 106, "loc": {"start": {"line": 106, "column": 4}, "end": {"line": 110, "column": 4}}}, "9": {"name": null, "line": 116, "loc": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 4}}}, "10": {"name": "implementation", "line": 128, "loc": {"start": {"line": 128, "column": 4}, "end": {"line": 133, "column": 4}}}, "11": {"name": "setImplementation", "line": 139, "loc": {"start": {"line": 139, "column": 4}, "end": {"line": 144, "column": 4}}}, "12": {"name": "_upgradeTo", "line": 150, "loc": {"start": {"line": 150, "column": 4}, "end": {"line": 155, "column": 4}}}, "13": {"name": "_fallback", "line": 157, "loc": {"start": {"line": 157, "column": 4}, "end": {"line": 174, "column": 4}}}, "14": {"name": "onlyProxyOwner", "line": 179, "loc": {"start": {"line": 179, "column": 4}, "end": {"line": 182, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 41}}, "2": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 46}}, "3": {"start": {"line": 54, "column": 8}, "end": {"line": 54, "column": 46}}, "4": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 45}}, "5": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 45}}, "6": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 75}}, "7": {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 62}}, "8": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 39}}, "9": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 36}}, "10": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 35}}, "11": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 71}}, "12": {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 60}}, "13": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 18}}, "14": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 49}}, "15": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 49}}, "16": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 56}}, "17": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 95}}, "18": {"start": {"line": 153, "column": 8}, "end": {"line": 153, "column": 43}}, "19": {"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": 40}}, "20": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 5570}}, "21": {"start": {"line": 159, "column": 12}, "end": {"line": 159, "column": 85}}, "22": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 40}}, "23": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 72}}, "24": {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 81}}}, "branchMap": {"1": {"line": 53, "type": "if", "locations": [{"start": {"line": 53, "column": 56}, "end": {"line": 53, "column": 56}}, {"start": {"line": 53, "column": 56}, "end": {"line": 53, "column": 56}}]}, "2": {"line": 85, "type": "if", "locations": [{"start": {"line": 85, "column": 61}, "end": {"line": 85, "column": 61}}, {"start": {"line": 85, "column": 61}, "end": {"line": 85, "column": 61}}]}, "3": {"line": 86, "type": "if", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 8}}, {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 8}}]}, "4": {"line": 95, "type": "if", "locations": [{"start": {"line": 95, "column": 57}, "end": {"line": 95, "column": 57}}, {"start": {"line": 95, "column": 57}, "end": {"line": 95, "column": 57}}]}, "5": {"line": 106, "type": "if", "locations": [{"start": {"line": 106, "column": 91}, "end": {"line": 106, "column": 91}}, {"start": {"line": 106, "column": 91}, "end": {"line": 106, "column": 91}}]}, "6": {"line": 109, "type": "if", "locations": [{"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 8}}, {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 8}}]}, "7": {"line": 152, "type": "if", "locations": [{"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 8}}, {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 8}}]}, "8": {"line": 158, "type": "if", "locations": [{"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 8}}, {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 8}}]}, "9": {"line": 159, "type": "if", "locations": [{"start": {"line": 159, "column": 12}, "end": {"line": 159, "column": 12}}, {"start": {"line": 159, "column": 12}, "end": {"line": 159, "column": 12}}]}, "10": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 8}}, {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 8}}]}, "11": {"line": 180, "type": "if", "locations": [{"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 8}}, {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 8}}]}}}}