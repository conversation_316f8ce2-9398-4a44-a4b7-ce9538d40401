const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("Netronlink Upgrade Tests", function () {
    let netronlink;
    let netronlinkV2;
    let owner, teamWallet, user1, user2;

    beforeEach(async function () {
        [owner, teamWallet, user1, user2] = await ethers.getSigners();

        // Deploy V1 contract using OpenZeppelin upgrades
        const NetronlinkV1 = await ethers.getContractFactory("Netronlink");
        
        netronlink = await upgrades.deployProxy(
            NetronlinkV1,
            [
                [user1.address, user2.address], // recipients
                [ethers.parseEther("10000000"), ethers.parseEther("5000000")], // amounts
                teamWallet.address // team wallet
            ],
            { 
                initializer: 'initialize',
                kind: 'transparent'
            }
        );
        await netronlink.waitForDeployment();
    });

    describe("Upgrade Functionality", function () {
        it("Should maintain state after upgrade", async function () {
            // Record state before upgrade
            const nameBeforeUpgrade = await netronlink.name();
            const symbolBeforeUpgrade = await netronlink.symbol();
            const totalSupplyBefore = await netronlink.totalSupply();
            const user1BalanceBefore = await netronlink.balanceOf(user1.address);
            const teamVestingBefore = await netronlink.teamVesting();
            const versionBefore = await netronlink.version();

            // Create V2 contract (same as V1 for this test)
            const NetronlinkV2 = await ethers.getContractFactory("Netronlink");
            
            // Perform upgrade
            netronlinkV2 = await upgrades.upgradeProxy(
                await netronlink.getAddress(),
                NetronlinkV2
            );

            // Verify state is maintained
            expect(await netronlinkV2.name()).to.equal(nameBeforeUpgrade);
            expect(await netronlinkV2.symbol()).to.equal(symbolBeforeUpgrade);
            expect(await netronlinkV2.totalSupply()).to.equal(totalSupplyBefore);
            expect(await netronlinkV2.balanceOf(user1.address)).to.equal(user1BalanceBefore);
            
            const teamVestingAfter = await netronlinkV2.teamVesting();
            expect(teamVestingAfter.totalAllocated).to.equal(teamVestingBefore.totalAllocated);
            expect(teamVestingAfter.claimed).to.equal(teamVestingBefore.claimed);
            expect(teamVestingAfter.startTime).to.equal(teamVestingBefore.startTime);
            
            expect(await netronlinkV2.version()).to.equal(versionBefore);
        });

        it("Should preserve functionality after upgrade", async function () {
            // Upgrade the contract
            const NetronlinkV2 = await ethers.getContractFactory("Netronlink");
            netronlinkV2 = await upgrades.upgradeProxy(
                await netronlink.getAddress(),
                NetronlinkV2
            );

            // Test that all functionality still works
            
            // 1. Transfer functionality
            const transferAmount = ethers.parseEther("1000");
            const burnAmount = transferAmount / 100n;
            const sendAmount = transferAmount - burnAmount;
            
            const initialBalance2 = await netronlinkV2.balanceOf(user2.address);
            await netronlinkV2.connect(user1).transfer(user2.address, transferAmount);
            expect(await netronlinkV2.balanceOf(user2.address)).to.equal(initialBalance2 + sendAmount);

            // 2. Whitelist functionality
            await netronlinkV2.addWhitelist(user1.address);
            
            // 3. Team vesting (fast forward to test claiming)
            await time.increase(365 * 24 * 60 * 60 + 30 * 24 * 60 * 60); // cliff + 1 month
            
            const claimableBefore = await netronlinkV2.connect(teamWallet).getClaimableTokens();
            expect(claimableBefore).to.be.greaterThan(0);
            
            await netronlinkV2.connect(teamWallet).claimTeamTokens();
            const teamBalance = await netronlinkV2.balanceOf(teamWallet.address);
            expect(teamBalance).to.equal(claimableBefore);
        });

        it("Should not allow double initialization after upgrade", async function () {
            // Upgrade the contract
            const NetronlinkV2 = await ethers.getContractFactory("Netronlink");
            netronlinkV2 = await upgrades.upgradeProxy(
                await netronlink.getAddress(),
                NetronlinkV2
            );

            // Try to initialize again - should fail
            await expect(
                netronlinkV2.initialize(
                    [user1.address],
                    [ethers.parseEther("1000000")],
                    teamWallet.address
                )
            ).to.be.reverted; // Should fail because already initialized
        });

        it("Should maintain ownership after upgrade", async function () {
            const ownerBefore = await netronlink.owner();
            
            // Upgrade the contract
            const NetronlinkV2 = await ethers.getContractFactory("Netronlink");
            netronlinkV2 = await upgrades.upgradeProxy(
                await netronlink.getAddress(),
                NetronlinkV2
            );

            expect(await netronlinkV2.owner()).to.equal(ownerBefore);
            
            // Verify owner can still perform owner functions
            await netronlinkV2.addWhitelist(user2.address);
            await netronlinkV2.removeWhitelist(user2.address);
        });
    });

    describe("Proxy Admin Access", function () {
        it("Should only allow admin to upgrade", async function () {
            const NetronlinkV2 = await ethers.getContractFactory("Netronlink");
            
            // Non-admin (user1) should not be able to upgrade
            await expect(
                upgrades.upgradeProxy(
                    await netronlink.getAddress(),
                    NetronlinkV2.connect(user1)
                )
            ).to.be.reverted;
        });
    });

    describe("Storage Layout Compatibility", function () {
        it("Should have proper storage gaps for future upgrades", async function () {
            // This test ensures the __gap array is properly sized
            // The gap should account for future storage variables
            
            // Deploy and check that the contract works properly
            // The presence of __gap[45] in the contract ensures storage layout compatibility
            
            const NetronlinkV2 = await ethers.getContractFactory("Netronlink");
            await upgrades.validateUpgrade(await netronlink.getAddress(), NetronlinkV2);
            
            // If validation passes, storage layout is compatible
            expect(true).to.be.true;
        });
    });
}); 