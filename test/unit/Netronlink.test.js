const { expect } = require("chai");
const { ethers, network } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("Netronlink Token Tests", function () {
    let netronlink;
    let owner, teamWallet, user1, user2, user3;
    const teamAmount = ethers.parseEther("20000000"); // 20M tokens
    const maxSupply = ethers.parseEther("80000000"); // 80M tokens
    const cliff = 365 * 24 * 60 * 60; // 365 days in seconds
    const monthly = 30 * 24 * 60 * 60; // 30 days in seconds
    const totalMonths = 24;

    beforeEach(async function () {
        [owner, teamWallet, user1, user2, user3] = await ethers.getSigners();

        // Deploy the contract as upgradeable
        const NetronlinkFactory = await ethers.getContractFactory("Netronlink");
        netronlink = await NetronlinkFactory.deploy();
        await netronlink.waitForDeployment();

        // Initialize with some initial distribution
        const recipients = [user1.address, user2.address];
        const amounts = [ethers.parseEther("10000000"), ethers.parseEther("5000000")]; // 10M, 5M

        await netronlink.initialize(recipients, amounts, teamWallet.address);
    });

    describe("1. Initialization Tests", function () {
        it("Should initialize correctly with valid parameters", async function () {
            // Check token details
            expect(await netronlink.name()).to.equal("Netronlink Token");
            expect(await netronlink.symbol()).to.equal("NTL");
            expect(await netronlink.owner()).to.equal(owner.address);
            expect(await netronlink.vestingRecipient()).to.equal(teamWallet.address);

            // Check initial balances
            expect(await netronlink.balanceOf(user1.address)).to.equal(ethers.parseEther("10000000"));
            expect(await netronlink.balanceOf(user2.address)).to.equal(ethers.parseEther("5000000"));
            expect(await netronlink.balanceOf(await netronlink.getAddress())).to.equal(teamAmount);

            // Check total supply
            const expectedTotalSupply = ethers.parseEther("35000000"); // 10M + 5M + 20M
            expect(await netronlink.totalSupply()).to.equal(expectedTotalSupply);
        });

        it("Should setup team vesting correctly", async function () {
            const teamVesting = await netronlink.teamVesting();
            expect(teamVesting.totalAllocated).to.equal(teamAmount);
            expect(teamVesting.claimed).to.equal(0);
            expect(teamVesting.startTime).to.be.greaterThan(0);
        });

        it("Should fail initialization with mismatched arrays", async function () {
            const NetronlinkFactory = await ethers.getContractFactory("Netronlink");
            const newContract = await NetronlinkFactory.deploy();
            
            const recipients = [user1.address, user2.address];
            const amounts = [ethers.parseEther("10000000")]; // Mismatched length

            await expect(
                newContract.initialize(recipients, amounts, teamWallet.address)
            ).to.be.revertedWith("Mismatched input arrays");
        });

        it("Should fail initialization when exceeding max supply", async function () {
            const NetronlinkFactory = await ethers.getContractFactory("Netronlink");
            const newContract = await NetronlinkFactory.deploy();
            
            const recipients = [user1.address];
            const amounts = [ethers.parseEther("80000001")]; // 80M + 1 wei > 80M max

            await expect(
                newContract.initialize(recipients, amounts, teamWallet.address)
            ).to.be.revertedWith("Exceeds max supply");
        });

        it("Should whitelist contract address by default", async function () {
            // Contract should be able to transfer without burn (indicates it's whitelisted)
            // We can test this by verifying contract balance equals team amount
            const contractAddress = await netronlink.getAddress();
            const contractBalance = await netronlink.balanceOf(contractAddress);
            expect(contractBalance).to.equal(teamAmount);
            
            // Contract being able to hold team tokens confirms it's whitelisted
        });
    });

    describe("2. Transfer and Burn Mechanism Tests", function () {
        beforeEach(async function () {
            // Add owner to whitelist for testing
            await netronlink.addWhitelist(owner.address);
        });

        it("Should transfer without burn between whitelisted addresses", async function () {
            await netronlink.addWhitelist(user1.address);
            await netronlink.addWhitelist(user2.address);

            const initialBalance1 = await netronlink.balanceOf(user1.address);
            const initialBalance2 = await netronlink.balanceOf(user2.address);
            const transferAmount = ethers.parseEther("1000");

            await netronlink.connect(user1).transfer(user2.address, transferAmount);

            expect(await netronlink.balanceOf(user1.address)).to.equal(initialBalance1 - transferAmount);
            expect(await netronlink.balanceOf(user2.address)).to.equal(initialBalance2 + transferAmount);
        });

        it("Should transfer without burn from whitelisted to non-whitelisted", async function () {
            await netronlink.addWhitelist(user1.address);
            // user2 is not whitelisted

            const initialBalance1 = await netronlink.balanceOf(user1.address);
            const initialBalance2 = await netronlink.balanceOf(user2.address);
            const transferAmount = ethers.parseEther("1000");

            await netronlink.connect(user1).transfer(user2.address, transferAmount);

            expect(await netronlink.balanceOf(user1.address)).to.equal(initialBalance1 - transferAmount);
            expect(await netronlink.balanceOf(user2.address)).to.equal(initialBalance2 + transferAmount);
        });

        it("Should transfer without burn from non-whitelisted to whitelisted", async function () {
            // user1 is not whitelisted
            await netronlink.addWhitelist(user2.address);

            const initialBalance1 = await netronlink.balanceOf(user1.address);
            const initialBalance2 = await netronlink.balanceOf(user2.address);
            const transferAmount = ethers.parseEther("1000");

            await netronlink.connect(user1).transfer(user2.address, transferAmount);

            expect(await netronlink.balanceOf(user1.address)).to.equal(initialBalance1 - transferAmount);
            expect(await netronlink.balanceOf(user2.address)).to.equal(initialBalance2 + transferAmount);
        });

        it("Should apply 1% burn for transfers between non-whitelisted addresses", async function () {
            // Neither user1 nor user3 are whitelisted
            const initialBalance1 = await netronlink.balanceOf(user1.address);
            const initialBalance3 = await netronlink.balanceOf(user3.address);
            const initialTotalSupply = await netronlink.totalSupply();
            
            const transferAmount = ethers.parseEther("1000");
            const burnAmount = transferAmount / 100n; // 1%
            const sendAmount = transferAmount - burnAmount;

            const tx = await netronlink.connect(user1).transfer(user3.address, transferAmount);

            expect(await netronlink.balanceOf(user1.address)).to.equal(initialBalance1 - transferAmount);
            expect(await netronlink.balanceOf(user3.address)).to.equal(initialBalance3 + sendAmount);
            expect(await netronlink.totalSupply()).to.equal(initialTotalSupply - burnAmount);

            // Check burn event
            await expect(tx).to.emit(netronlink, "TokensBurned").withArgs(user1.address, burnAmount);
        });

        it("Should fail transfer to zero address", async function () {
            const transferAmount = ethers.parseEther("1000");
            
            await expect(
                netronlink.connect(user1).transfer(ethers.ZeroAddress, transferAmount)
            ).to.be.revertedWith("ERC20: transfer to the zero address");
        });

        it("Should handle zero amount transfers correctly", async function () {
            const initialBalance1 = await netronlink.balanceOf(user1.address);
            const initialBalance2 = await netronlink.balanceOf(user2.address);

            await netronlink.connect(user1).transfer(user2.address, 0);

            expect(await netronlink.balanceOf(user1.address)).to.equal(initialBalance1);
            expect(await netronlink.balanceOf(user2.address)).to.equal(initialBalance2);
        });
    });

    describe("3. Whitelist Management Tests", function () {
        it("Should allow owner to add addresses to whitelist", async function () {
            const tx = await netronlink.addWhitelist(user3.address);
            
            await expect(tx).to.emit(netronlink, "Whitelisted").withArgs(user3.address);
            
            // Give user3 some tokens first, then test that user3 is now whitelisted (no burn on transfer)
            await netronlink.connect(user1).transfer(user3.address, ethers.parseEther("1000"));
            
            const transferAmount = ethers.parseEther("100");
            const initialBalance2 = await netronlink.balanceOf(user2.address);
            
            await netronlink.connect(user3).transfer(user2.address, transferAmount);
            expect(await netronlink.balanceOf(user2.address)).to.equal(initialBalance2 + transferAmount);
        });

        it("Should allow owner to remove addresses from whitelist", async function () {
            // First add to whitelist
            await netronlink.addWhitelist(user1.address);
            
            // Then remove
            const tx = await netronlink.removeWhitelist(user1.address);
            
            await expect(tx).to.emit(netronlink, "WhitelistRemoved").withArgs(user1.address);
            
            // Test that user1 is no longer whitelisted (burn should apply)
            const transferAmount = ethers.parseEther("100");
            const burnAmount = transferAmount / 100n;
            const sendAmount = transferAmount - burnAmount;
            const initialBalance2 = await netronlink.balanceOf(user2.address);
            
            await netronlink.connect(user1).transfer(user2.address, transferAmount);
            expect(await netronlink.balanceOf(user2.address)).to.equal(initialBalance2 + sendAmount);
        });

        it("Should not allow non-owner to modify whitelist", async function () {
            await expect(
                netronlink.connect(user1).addWhitelist(user3.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");

            await expect(
                netronlink.connect(user1).removeWhitelist(user2.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });
    });

    describe("4. Team Vesting and Claiming Tests", function () {
        it("Should not allow claiming before cliff period", async function () {
            await expect(
                netronlink.connect(teamWallet).claimTeamTokens()
            ).to.be.revertedWith("Nothing to claim");
        });

        it("Should not allow unauthorized users to claim", async function () {
            // Fast forward past cliff
            await time.increase(cliff + monthly);
            
            await expect(
                netronlink.connect(user1).claimTeamTokens()
            ).to.be.revertedWith("Not authorized");
        });

        it("Should allow claiming after cliff + 1 month", async function () {
            // Fast forward past cliff + 1 month
            await time.increase(cliff + monthly);
            
            const expectedClaimable = teamAmount / BigInt(totalMonths); // 1/24 of total
            const initialBalance = await netronlink.balanceOf(teamWallet.address);
            
            const tx = await netronlink.connect(teamWallet).claimTeamTokens();
            
            expect(await netronlink.balanceOf(teamWallet.address)).to.equal(initialBalance + expectedClaimable);
            
            await expect(tx).to.emit(netronlink, "TeamTokensClaimed")
                .withArgs(teamWallet.address, expectedClaimable);
        });

        it("Should handle progressive monthly claims correctly", async function () {
            // Fast forward past cliff + 3 months
            await time.increase(cliff + (monthly * 3));
            
            const expectedClaimable = (teamAmount * 3n) / BigInt(totalMonths); // 3/24 of total
            
            await netronlink.connect(teamWallet).claimTeamTokens();
            
            // Check vesting state
            const teamVesting = await netronlink.teamVesting();
            expect(teamVesting.claimed).to.equal(expectedClaimable);
            
            // Fast forward 2 more months
            await time.increase(monthly * 2);
            
            const additionalClaimable = (teamAmount * 2n) / BigInt(totalMonths); // 2/24 more
            const initialBalance = await netronlink.balanceOf(teamWallet.address);
            
            await netronlink.connect(teamWallet).claimTeamTokens();
            
            expect(await netronlink.balanceOf(teamWallet.address)).to.equal(initialBalance + additionalClaimable);
        });

        it("Should allow full claiming after complete vesting period", async function () {
            // Fast forward past cliff + 24 months
            await time.increase(cliff + (monthly * totalMonths));
            
            const initialBalance = await netronlink.balanceOf(teamWallet.address);
            
            await netronlink.connect(teamWallet).claimTeamTokens();
            
            expect(await netronlink.balanceOf(teamWallet.address)).to.equal(initialBalance + teamAmount);
            
            // Verify no more tokens can be claimed
            await expect(
                netronlink.connect(teamWallet).claimTeamTokens()
            ).to.be.revertedWith("Nothing to claim");
        });

        it("Should not allow over-claiming", async function () {
            // Fast forward past cliff + 25 months (beyond vesting period)
            await time.increase(cliff + (monthly * 25));
            
            const initialBalance = await netronlink.balanceOf(teamWallet.address);
            
            await netronlink.connect(teamWallet).claimTeamTokens();
            
            // Should only get the full team amount, not more
            expect(await netronlink.balanceOf(teamWallet.address)).to.equal(initialBalance + teamAmount);
        });

        it("Should return correct claimable amounts", async function () {
            // Before cliff
            expect(await netronlink.connect(teamWallet).getClaimableTokens()).to.equal(0);
            
            // After cliff + 6 months
            await time.increase(cliff + (monthly * 6));
            
            const expectedClaimable = (teamAmount * 6n) / BigInt(totalMonths);
            expect(await netronlink.connect(teamWallet).getClaimableTokens()).to.equal(expectedClaimable);
        });

        it("Should not allow unauthorized users to check claimable tokens", async function () {
            await expect(
                netronlink.connect(user1).getClaimableTokens()
            ).to.be.revertedWith("Not authorized");
        });
    });

    describe("5. View Functions and Utility Tests", function () {
        it("Should return correct version", async function () {
            expect(await netronlink.version()).to.equal("1.0.0");
        });

        it("Should have correct constants", async function () {
            expect(await netronlink.MAX_SUPPLY()).to.equal(maxSupply);
            expect(await netronlink.CLIFF()).to.equal(cliff);
            expect(await netronlink.MONTHLY()).to.equal(monthly);
            expect(await netronlink.TOTAL_MONTHS()).to.equal(totalMonths);
        });
    });

    describe("6. Edge Cases and Security Tests", function () {
        it("Should handle large transfers correctly", async function () {
            const largeAmount = ethers.parseEther("9999999"); // Close to user1's balance
            const burnAmount = largeAmount / 100n;
            const sendAmount = largeAmount - burnAmount;
            
            const initialBalance2 = await netronlink.balanceOf(user2.address);
            const initialTotalSupply = await netronlink.totalSupply();
            
            await netronlink.connect(user1).transfer(user2.address, largeAmount);
            
            expect(await netronlink.balanceOf(user2.address)).to.equal(initialBalance2 + sendAmount);
            expect(await netronlink.totalSupply()).to.equal(initialTotalSupply - burnAmount);
        });

        it("Should handle precision in vesting calculations", async function () {
            // Test edge case where division might have remainder
            await time.increase(cliff + (monthly * 13)); // 13 months
            
            const claimable = await netronlink.connect(teamWallet).getClaimableTokens();
            const expectedClaimable = (teamAmount * 13n) / BigInt(totalMonths);
            
            expect(claimable).to.equal(expectedClaimable);
        });

        it("Should maintain total supply integrity through multiple operations", async function () {
            const initialTotalSupply = await netronlink.totalSupply();
            
            // Perform multiple transfers with burns
            await netronlink.connect(user1).transfer(user3.address, ethers.parseEther("100"));
            await netronlink.connect(user2).transfer(user3.address, ethers.parseEther("200"));
            
            const expectedBurn = ethers.parseEther("3"); // 1% of 100 + 1% of 200
            const currentTotalSupply = await netronlink.totalSupply();
            
            expect(currentTotalSupply).to.equal(initialTotalSupply - expectedBurn);
        });
    });
}); 