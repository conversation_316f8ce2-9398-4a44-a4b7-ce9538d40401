const { ethers, run, network } = require("hardhat");

/**
 * Verify upgradeable contracts deployment
 * @param {string} implementationAddress - Address of the implementation contract
 * @param {string} proxyAddress - Address of the proxy contract
 * @param {array} recipients - Array of recipient addresses used in initialization
 * @param {array} amounts - Array of token amounts used in initialization
 * @param {string} teamFounderAddress - Team/founder address used in initialization
 */
async function verifyUpgradeableContracts(implementationAddress, proxyAddress, recipients, amounts, teamFounderAddress) {
    console.log("🔍 Verifying upgradeable contracts...");
    console.log("Network:", network.name);

    if (!implementationAddress || !proxyAddress) {
        throw new Error("❌ Missing contract addresses for verification");
    }

    try {
        // Wait a bit for contract deployment to be indexed
        console.log("⏳ Waiting for contracts to be indexed...");
        await new Promise(resolve => setTimeout(resolve, 30000)); // 30 second delay

        // Verify Implementation Contract
        console.log("\n🔨 Verifying Netronlink Implementation...");
        console.log("Address:", implementationAddress);
        
        await run("verify:verify", {
            address: implementationAddress,
            constructorArguments: [],
            contract: "contracts/Netronlink.sol:Netronlink"
        });
        console.log("✅ Implementation contract verified!");

        // Verify Proxy Contract
        console.log("\n🔨 Verifying Proxy Contract...");
        console.log("Address:", proxyAddress);
        
        await run("verify:verify", {
            address: proxyAddress,
            constructorArguments: [],
            contract: "contracts/OwnedUpgradeabilityProxy.sol:OwnedUpgradeabilityProxy"
        });
        console.log("✅ Proxy contract verified!");

        // Verify contract functionality
        console.log("\n🔍 Verifying contract functionality...");
        const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);
        
        const name = await netronlink.name();
        const symbol = await netronlink.symbol();
        const totalSupply = await netronlink.totalSupply();
        const owner = await netronlink.owner();
        const maxSupply = await netronlink.MAX_SUPPLY();

        console.log("✅ Contract functionality verified:");
        console.log("  Token Name:", name);
        console.log("  Token Symbol:", symbol);
        console.log("  Total Supply:", ethers.formatEther(totalSupply), "NTL");
        console.log("  Max Supply:", ethers.formatEther(maxSupply), "NTL");
        console.log("  Contract Owner:", owner);

        // Verify some recipient balances
        console.log("\n🔍 Verifying token distribution...");
        for (let i = 0; i < Math.min(3, recipients.length); i++) {
            const balance = await netronlink.balanceOf(recipients[i]);
            console.log(`  Recipient ${i + 1} (${recipients[i]}): ${ethers.formatEther(balance)} NTL`);
        }

        console.log("\n📄 Verification Summary:");
        console.log("========================================");
        console.log("🏗️  Network:", network.name);
        console.log("🎯 Netronlink Implementation:", implementationAddress);
        console.log("🎯 Netronlink Proxy (Main):", proxyAddress);
        console.log("🔗 Explorer:", getExplorerUrl(network.name));
        console.log("========================================");

        console.log("\n📋 Contract Interaction Links:");
        console.log("Main Contract (Proxy):", `${getExplorerUrl(network.name)}/address/${proxyAddress}`);
        console.log("Implementation:", `${getExplorerUrl(network.name)}/address/${implementationAddress}`);

        return {
            success: true,
            implementationAddress,
            proxyAddress,
            tokenInfo: {
                name,
                symbol,
                totalSupply: ethers.formatEther(totalSupply),
                maxSupply: ethers.formatEther(maxSupply),
                owner
            }
        };

    } catch (error) {
        console.error("❌ Verification failed:", error.message);
        
        if (error.message.includes("Already Verified")) {
            console.log("ℹ️  Contract was already verified");
            return {
                success: true,
                alreadyVerified: true,
                implementationAddress,
                proxyAddress
            };
        } else if (error.message.includes("does not have bytecode")) {
            console.error("❌ Contract address does not exist or has no bytecode");
        } else if (error.message.includes("Fail - Unable to verify")) {
            console.error("❌ Unable to verify contract. Check if the constructor arguments match deployment");
        }
        
        throw error;
    }
}

/**
 * Get explorer URL based on network name
 */
function getExplorerUrl(networkName) {
    const explorerUrls = {
        mainnet: "https://etherscan.io",
        goerli: "https://goerli.etherscan.io",
        sepolia: "https://sepolia.etherscan.io",
        bsc: "https://bscscan.com",
        bscTestnet: "https://testnet.bscscan.com",
        polygon: "https://polygonscan.com",
        mumbai: "https://mumbai.polygonscan.com",
        avalanche: "https://snowtrace.io",
        fuji: "https://testnet.snowtrace.io",
        arbitrum: "https://arbiscan.io",
        arbitrumGoerli: "https://goerli.arbiscan.io",
        optimism: "https://optimistic.etherscan.io",
        optimismGoerli: "https://goerli-optimism.etherscan.io"
    };
    
    return explorerUrls[networkName] || "https://etherscan.io";
}

// Export for external use
module.exports = { verifyUpgradeableContracts };

// If run directly
if (require.main === module) {
    console.log("❌ This script should be called from the deployment script or with proper parameters");
    console.log("Usage: require('./verify-upgradeable').verifyUpgradeableContracts(impl, proxy, recipients, amounts, teamAddress)");
    process.exit(1);
} 