{"author": "<PERSON><PERSON><PERSON>", "scripts": {"deploy": "npx hardhat deploy --tags upgradeable", "verify": "npx hardhat run scripts/verify-upgradeable.js", "compile": "npx hardhat compile", "test": "npx hardhat test", "coverage": "npx hardhat coverage"}, "devDependencies": {"@chainlink/contracts": "^1.3.0", "@nomicfoundation/hardhat-chai-matchers": "^2.0.8", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@nomicfoundation/hardhat-ignition": "^0.15.9", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.9", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.12", "@nomiclabs/hardhat-ethers": "^2.2.3", "@openzeppelin/hardhat-upgrades": "^3.9.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@uniswap/v2-core": "^1.0.1", "@uniswap/v2-periphery": "^1.1.0-beta.0", "chai": "4", "dotenv": "^16.4.7", "ethers": "^6.13.5", "hardhat": "^2.22.18", "hardhat-deploy": "^0.14.0", "hardhat-gas-reporter": "^2.2.2", "solidity-coverage": "^0.8.14", "typechain": "^8.3.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "dependencies": {"prettier": "^3.5.3", "solhint": "^5.0.5"}, "version": "0.0.0"}